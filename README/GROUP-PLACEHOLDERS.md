# Group Placeholders

## Overview

The Group Placeholder system provides a consistent, visually appealing default display for challenge items when no photo or image is provided. Instead of showing blank spaces or generic text, challenges now display their challenge number (#1, #2, #3, etc.) in large, readable typography with alternating client brand colors.

## Features

### Visual Design
- **Large Typography**: Group position numbers displayed in bold, readable fonts
- **Alternating Colors**: Primary and secondary brand colors alternate between consecutive groups
- **Consistent Sizing**: Multiple size variants (small, medium, large) to match different contexts
- **Theme Support**: Full dark/light theme compatibility
- **Accessibility**: Proper ARIA labels and semantic markup

### Implementation
- **Reusable Component**: Single `GroupPlaceholder` component used across all group contexts
- **Automatic Alternation**: Even group IDs use primary colors, odd group IDs use secondary colors
- **Responsive Design**: Scales appropriately across different screen sizes
- **Performance Optimized**: Lightweight component with minimal re-renders

## Usage

### Basic Usage
```jsx
import GroupPlaceholder from '../common/GroupPlaceholder';

// For challenge display
<GroupPlaceholder
  groupId={1}
  groupName="Level 1"
  size="medium"
  challengeNumber={3}
  primaryColor="#00A3E3"
/>
```

### Props
- `groupId` (number, required): The group's ID for color alternation
- `groupName` (string, optional): The group's name for accessibility labels
- `size` (string, optional): Size variant - 'small', 'medium', or 'large' (default: 'medium')
- `theme` (string, optional): Theme override - uses ThemeProvider context if not provided
- `challengeNumber` (number, optional): The challenge number to display (#1, #2, etc.)
- `primaryColor` (string, optional): Client's primary/highlight color
- `secondaryColor` (string, optional): Client's secondary color
- `isLevel` (boolean, optional): Whether this represents a level vs group
- `groupIndex` (number, optional): Group index for color alternation

### Size Variants
- **Small**: 64x64px (w-16 h-16) - For compact displays
- **Medium**: 96x96px (w-24 h-24) - Default size for most contexts
- **Large**: 115x115px (w-[115px] h-[115px]) - For prominent displays

## Color Scheme

### Alternating Pattern
- **Even Group IDs (0, 2, 4, ...)**: Primary brand colors
- **Odd Group IDs (1, 3, 5, ...)**: Secondary brand colors

### Theme Support
- **Dark Theme**: Uses CSS custom properties for primary/secondary colors
- **Light Theme**: Automatically adjusts colors for proper contrast
- **Brand Colors**: Respects client-specific color schemes

## Current Implementation

### Challenge Groups
The GroupPlaceholder is currently implemented in:
- **Main Challenge Grid**: Primary challenge display circles on the main screen
- **Challenge Detail Modal**: Large challenge display in modal overlays
- **Challenge Corner Display**: Secondary challenge display in modal corners

### Integration Points
- `src/components/Challenges/Challenges.jsx`: Main challenge grid and modal displays
- `src/components/common/GroupPlaceholder/`: Reusable component implementation

## Benefits

### User Experience
- **Visual Hierarchy**: Clear distinction between different groups
- **Professional Appearance**: Polished look instead of empty placeholders
- **Accessibility**: Screen reader friendly with proper labels
- **Consistency**: Uniform appearance across all group contexts

### Developer Experience
- **Reusable**: Single component for all group placeholder needs
- **Maintainable**: Centralized styling and behavior
- **Extensible**: Easy to add new size variants or styling options
- **Type Safe**: Clear prop interface with sensible defaults

## Future Enhancements

### Potential Additions
- **Custom Icons**: Option to display icons instead of numbers
- **Animation**: Subtle hover or loading animations
- **Custom Colors**: Override colors for specific group types
- **Badge Variants**: Different shapes (square, rounded rectangle, etc.)

### Additional Contexts
- **Decision Groups**: Could be extended to decision group displays
- **User Groups**: Team or department groupings
- **Category Groups**: Product or service category displays

## Technical Details

### Dependencies
- React (functional component with hooks)
- ThemeProvider context for theme detection
- Tailwind CSS for styling

### Performance
- Lightweight component (~2KB)
- No external dependencies beyond React and theme context
- Optimized for minimal re-renders

### Browser Support
- Modern browsers with CSS Grid and Flexbox support
- Responsive design works on all screen sizes
- Accessible across all major screen readers
