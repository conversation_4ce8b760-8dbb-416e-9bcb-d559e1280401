import { db } from '../db/index.js';
import { apiClient } from '../lib/index.js';
import {
  createDecisionScheme,
  createDecisionPage,
  listDecisionSchemes,
  findDecisionSchemeById,
  findDecisionPagesBySchemeId,
  updateDecisionSchemeWithPages,
} from '../db/queries/decision.js';

const DECISION_CONTROLLER = {
  create: async (req, res, next) => {
    const { name, pnl, decisions, globalTemplate, useCustomGlobalTemplate } = req.body;

    try {
      const result = await db.transaction(async () => {
        // Create the scheme first with PNL data
        const scheme = await createDecisionScheme(name, pnl, globalTemplate, useCustomGlobalTemplate);

        // Then create pages using the scheme ID
        const pages = [];
        for (let i = 0; i < decisions.length; i++) {
          const decision = decisions[i];
          const page = await createDecisionPage(
            scheme.scheme_id,
            i + 1,
            decision.page_name || `Page ${i + 1}`,  // Use the page_name from the request
            decision.sliders,
            decision.incentives,
            decision.pnl,
            decision.page_image || null,
            decision.page_pnl_prompt_template || null,
            decision.use_custom_page_template || false
          );
          pages.push(page);
        }

        return {
          id: scheme.scheme_id,
          name: scheme.scheme_name,
          pnl: scheme.scheme_pnl,
          created_at: scheme.scheme_created_at,
          pages: pages.map((page) => ({
            id: page.page_id,
            page_number: page.page_number,
            page_name: page.page_name,
            page_image: page.page_image,
            sliders: page.sliders,
            incentives: page.incentives,
            pnl: page.page_pnl,
          })),
        };
      });

      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error('Error creating decision:', error);
      return apiClient.serverError(req, res, next);
    }
  },

  list: async (req, res, next) => {
    const {
      query = '',
      offset = 0,
      limit = 15,
      sort = 'name',
      ascending = 'true',
      showDisabled = 'false',
    } = req.query;

    try {
      const result = await listDecisionSchemes(db, {
        query,
        offset: parseInt(offset),
        limit: parseInt(limit),
        sort,
        ascending: ascending === 'true',
        showDisabled: showDisabled === 'true',
      });

      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  get: async (req, res, next) => {
    const { id } = req.params;

    try {
      // Get the decision scheme
      const scheme = await findDecisionSchemeById(id);

      if (!scheme) {
        return apiClient.notFound(req, res, next, 'Decision scheme not found');
      }

      // Get all pages for this decision
      const pages = await findDecisionPagesBySchemeId(scheme.id);
      const result = {
        id: scheme.id,
        name: scheme.name,
        pnl: scheme.pnl || null,
        global_pnl_prompt_template: scheme.global_pnl_prompt_template,
        use_custom_global_template: scheme.use_custom_global_template,
        created_at: scheme.created_at,
        updated_at: scheme.updated_at,
        is_disabled: scheme.disabled || false,
        pages: (pages || []).map((page) => ({
          id: page.id,
          page_number: page.page_number,
          page_name: page.page_name,
          page_image: page.page_image,
          sliders: page.sliders || [],
          incentives: page.incentives || [],
          page_pnl: page.page_pnl,
          page_pnl_prompt_template: page.page_pnl_prompt_template,
          use_custom_page_template: page.use_custom_page_template,
        })),
      };

      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error('Error in get decision:', error);
      return apiClient.serverError(req, res, next);
    }
  },

  update: async (req, res, next) => {
    const { id, name, pnl, pages, globalTemplate, useCustomGlobalTemplate } = req.body;

    try {
      const result = await updateDecisionSchemeWithPages(id, name, pnl, pages, globalTemplate, useCustomGlobalTemplate);

      return apiClient.success(req, res, next, {
        id: result.scheme.scheme_id,
        name: result.scheme.scheme_name,
        pnl: result.scheme.scheme_pnl,
        global_pnl_prompt_template: result.scheme.global_pnl_prompt_template,
        use_custom_global_template: result.scheme.use_custom_global_template,
        updated_at: result.scheme.scheme_updated_at,
        pages: result.pages.map((page) => ({
          id: page.page_id,
          page_number: page.page_number,
          page_name: page.page_name,
          page_image: page.page_image,
          sliders: page.sliders,
          incentives: page.incentives,
          pnl: page.page_pnl,
          page_pnl_prompt_template: page.page_pnl_prompt_template,
          use_custom_page_template: page.use_custom_page_template,
        })),
      });
    } catch (error) {
      console.error('Error updating decision:', error);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default DECISION_CONTROLLER;
