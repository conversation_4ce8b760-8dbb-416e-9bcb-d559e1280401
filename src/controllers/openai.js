import OpenAI from 'openai';
import apiClient from '../lib/api_client.js';
import CONFIG from '../../config.json' with { type: 'json' };
import { SYSTEM_PROMPTS, generatePNLAnalysisPrompt, getPromptTemplate } from '../utils/prompts.js';
import { findDecisionSchemeById, findDecisionPagesBySchemeId } from '../db/queries/decision.js';
import { createAiPromptLog } from '../db/queries/aiPromptLogs.js';

const openai = new OpenAI({
  apiKey: CONFIG.OPENAI_API_KEY,
});

const cleanJsonResponse = (response) => {
  // Remove markdown code block syntax if present
  return response
    .replace(/^```json\n/, '')  // Remove opening ```json
    .replace(/^```\n/, '')      // Remove opening ``` without json
    .replace(/\n```$/, '')      // Remove closing ```
    .trim();                    // Remove any extra whitespace
};

const OPENAI_CONTROLLER = {
  analyzePNL: async (req, res, next) => {
    try {
      const { pnlDataArray, userSelections, schemeId, pages } = req.body;

      // Validate input
      if (!Array.isArray(pnlDataArray)) {
        return apiClient.invalidRequest(req, res, next, 'pnlDataArray must be an array');
      }

      if (pnlDataArray.length === 0) {
        return apiClient.invalidRequest(req, res, next, 'pnlDataArray cannot be empty');
      }

      // Fetch scheme and page data for custom templates
      let schemeData = null;
      let pagesData = null;

      if (schemeId) {
        try {
          schemeData = await findDecisionSchemeById(schemeId);
          if (schemeData) {
            pagesData = await findDecisionPagesBySchemeId(schemeId);
          }
        } catch (dbError) {
          console.warn('Failed to fetch scheme/page data for custom templates:', dbError);
          // Continue with default templates if database fetch fails
        }
      }

      // Process all PnL data objects in parallel for better performance
      const analysisPromises = pnlDataArray.map(async (pnlData, i) => {
        try {
          const systemPrompt = SYSTEM_PROMPTS.FINANCIAL_ANALYST;
          const customTemplate = getPromptTemplate(schemeData, pagesData, i, pages);
          const questionPrompt = generatePNLAnalysisPrompt({
            pnlData,
            userSelections,
            customTemplate,
            templateVariables: {
              pageName: pages && pages[i] ? pages[i].page_name : `Page ${i + 1}`,
              schemeName: schemeData ? schemeData.name : 'Unknown Scheme'
            }
          });

          const completion = await openai.chat.completions.create({
            model: "gpt-4-turbo-preview",
            messages: [
              {
                role: "system",
                content: systemPrompt
              },
              {
                role: "user",
                content: questionPrompt
              }
            ],
            temperature: 0.2,
          });

          // Log the AI interaction
          const inputSources = [
            {
              type: 'pnl_data',
              page_index: i,
              page_name: pages && pages[i] ? pages[i].page_name : `Page ${i + 1}`,
            },
            {
              type: 'user_selections',
              total_fte: userSelections.totalFTE,
              total_investment: userSelections.totalInvestment,
            },
            {
              type: 'scheme_data',
              scheme_id: schemeId,
              scheme_name: schemeData ? schemeData.name : null,
              has_custom_template: schemeData ? schemeData.use_custom_global_template : false,
            },
            {
              type: 'page_data',
              page_id: pagesData && pagesData[i] ? pagesData[i].id : null,
              has_custom_template: pagesData && pagesData[i] ? pagesData[i].use_custom_page_template : false,
            }
          ];

          // Log AI interaction (don't await to avoid blocking)
          createAiPromptLog({
            finalSystemPrompt: systemPrompt,
            finalQuestionPrompt: questionPrompt,
            tokensUsed: completion.usage?.total_tokens || null,
            inputSources: inputSources
          }).catch(logError => {
            console.warn('Failed to log AI prompt interaction:', logError);
            // Don't fail the main request if logging fails
          });

          // Parse and validate response
          const responseContent = cleanJsonResponse(completion.choices[0].message.content);
          const parsedResponse = JSON.parse(responseContent);

          // Validate response structure
          if (!parsedResponse.updatedPnL || !parsedResponse.analysis) {
            throw new Error('Invalid response structure');
          }

          // Validate updatedPnL is an array
          if (!Array.isArray(parsedResponse.updatedPnL)) {
            throw new Error(`updatedPnL must be an array, but received: ${typeof parsedResponse.updatedPnL} - ${JSON.stringify(parsedResponse.updatedPnL)}`);
          }

          // Validate each row in updatedPnL is also an array
          if (!parsedResponse.updatedPnL.every(row => Array.isArray(row))) {
            const invalidRows = parsedResponse.updatedPnL.filter(row => !Array.isArray(row));
            throw new Error(`Each row in updatedPnL must be an array. Invalid rows: ${JSON.stringify(invalidRows)}`);
          }

          return parsedResponse;

        } catch (error) {
          console.error(`OpenAI API Error for PnL ${i}:`, error);
          throw new Error(`Failed to analyze PnL ${i}: ${error.message}`);
        }
      });

      // Wait for all analyses to complete in parallel
      const analysisResults = await Promise.all(analysisPromises);

      return apiClient.success(req, res, next, {
        analyses: analysisResults
      });
    } catch (error) {
      console.error('OpenAI Analysis Error:', error);
      return apiClient.serverError(req, res, next);
    }
  },
  analyzeTable: async (req, res, next) => {
    try {
      const { data } = req.body;

      const systemPrompt = `You are a financial data analyzer. Analyze the provided table data and identify key financial metrics.
            Return ONLY a JSON object with the following structure:
            {
              "keyMetrics": {
                "revenue": { "original": number, "updated": number },
                "grossProfit": { "original": number, "updated": number },
                "operatingIncome": { "original": number, "updated": number },
              }
            }
            For each metric, extract both the original and updated values from the table. The table has three columns:
            description, original amount, and updated amount. Identify equivalent terms (e.g., "Sales" for "Revenue").`;

      const questionPrompt = JSON.stringify(data);

      const completion = await openai.chat.completions.create({
        model: "gpt-4-turbo-preview",
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: questionPrompt
          }
        ],
        temperature: 0.1,
      });

      // Log the AI interaction
      const inputSources = [
        {
          type: 'table_data',
          data_size: Array.isArray(data) ? data.length : 'unknown',
          analysis_type: 'financial_metrics_extraction'
        }
      ];

      try {
        await createAiPromptLog({
          finalSystemPrompt: systemPrompt,
          finalQuestionPrompt: questionPrompt,
          tokensUsed: completion.usage?.total_tokens || null,
          inputSources: inputSources
        });
      } catch (logError) {
        console.warn('Failed to log AI table analysis interaction:', logError);
        // Don't fail the main request if logging fails
      }

      let parsedResponse;
      try {
        const responseContent = cleanJsonResponse(completion.choices[0].message.content);
        parsedResponse = JSON.parse(responseContent);
        
        // Validate response structure
        if (!parsedResponse.keyMetrics) {
          throw new Error('Invalid response structure: missing keyMetrics');
        }

        const requiredMetrics = ['revenue', 'grossProfit', 'operatingIncome'];
        for (const metric of requiredMetrics) {
          if (!parsedResponse.keyMetrics[metric]) {
            throw new Error(`Invalid response structure: missing ${metric}`);
          }
          if (!('original' in parsedResponse.keyMetrics[metric]) || 
              !('updated' in parsedResponse.keyMetrics[metric])) {
            throw new Error(`Invalid response structure: missing original/updated values for ${metric}`);
          }
        }

      } catch (parseError) {
        console.error('OpenAI Response Parse Error:', parseError);
        console.error('Raw Response:', completion.choices[0].message.content);
        return apiClient.serverError(req, res, next, 'Failed to parse AI response');
      }

      return apiClient.success(req, res, next, parsedResponse);
    } catch (error) {
      console.error('OpenAI Table Analysis Error:', error);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default OPENAI_CONTROLLER;
