import { apiClient } from '../lib/index.js';
import {
  findChallengeGroupById,
  createChallengeGroup,
  deleteChallengeGroup,
  listAllChallengeGroups,
  listChallengeGroupsByScheme,
} from '../db/queries/challengeGroups.js';

const CHALLENGE_GROUPS_CONTROLLER = {
  get: async (req, res, next) => {
    try {
      const group = await findChallengeGroupById(req.params.id);

      if (!group) {
        return apiClient.notFound(req, res, next, 'Challenge Group not found');
      }

      return apiClient.success(req, res, next, group);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  create: async (req, res, next) => {
    const { name, challenge_scheme_id } = req.body;

    if (!challenge_scheme_id) {
      return apiClient.clientError(req, res, next, 'challenge_scheme_id is required');
    }

    try {
      const group = await createChallengeGroup({ name, challenge_scheme_id });

      return apiClient.success(req, res, next, group);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  delete: async (req, res, next) => {
    const { id } = req.params;

    try {
      const group = await deleteChallengeGroup(id);

      if (!group) {
        return apiClient.notFound(req, res, next, 'Challenge Group not found');
      }

      return apiClient.success(req, res, next, group);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  listAll: async (req, res, next) => {
    try {
      const groups = await listAllChallengeGroups();

      return apiClient.success(req, res, next, groups);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },

  listByScheme: async (req, res, next) => {
    const { challenge_scheme_id } = req.params;

    if (!challenge_scheme_id) {
      return apiClient.clientError(req, res, next, 'challenge_scheme_id is required');
    }

    try {
      const groups = await listChallengeGroupsByScheme(parseInt(challenge_scheme_id));

      return apiClient.success(req, res, next, groups);
    } catch (e) {
      console.error(e);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default CHALLENGE_GROUPS_CONTROLLER;
