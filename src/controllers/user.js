import jwt from 'jsonwebtoken';
import { apiClient } from '../lib/index.js';
import config from '../../config.json' with { type: 'json' };
import {
  findUserByEmailAndPassword,
  createUser,
  findUserById,
  updateUserPassword,
  findUserByEmail,
  updateTeamDetails,
} from '../db/queries/user.js';
import sgMail from '@sendgrid/mail';
import {
  lumenTeam,
  lumenTeamRevenueNumber,
  lumenTeamMetric,
  teamClients,
  teamGoals,
  clients,
  lumenInitiativeScheme,
  lumenInitiative,
  lumenTeamSelectedInitiative,
  lumenTeamSelectedChallenge,
  lumenChallengeScheme,
  lumenChallenge,
  leaderboardScheme,
  leaderboardRegion,
  leaderboardUser,
  orgChart,
  orgChartScheme,
  orgChartType,
  orgChartUser,
  teamSelectedOrgChart,
  selfAssessmentAnswers,
  globalTeamMetric,
  globalTeamMetricsScheme,
  welcomePage,
  challengeGroups,
} from '../db/schema.js';
import { eq, and, inArray, sql } from 'drizzle-orm';
import { db } from '../db/index.js';
import { TeamModel, TeamClientsModel, TeamGoalsModel, TeamMetricsModel } from '../models/index.js';
import { findDecisionPagesBySchemeId, findDecisionSchemeById } from '../db/queries/decision.js';

sgMail.setApiKey(config.SENDGRID_API_KEY);

const USER_CONTROLLER = {
  signIn: async (req, res, next) => {
    try {
      const user = await findUserByEmailAndPassword(
        req.body.email,
        req.body.password
      );

      if (!user) {
        setTimeout(() => {
          return apiClient.invalidRequest(req, res, next);
        }, 1000);
      } else {
        const userObj = {
          id: user.team_id,
          email: user.team_email,
          clientId: user.client_id,
        };
        const jwtToken = jwt.sign(userObj, config.jwtPassword, {
          expiresIn: '24h',
        });

        return apiClient.success(req, res, next, {
          token: jwtToken,
          user: userObj,
        });
      }
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  signUp: async (req, res, next) => {
    const { name, email, password } = req.body;

    try {
      // Check if team already exists
      const existingTeam = await TeamModel.findByEmail(email);

      if (existingTeam) {
        return apiClient.invalidRequest(
          req,
          res,
          next,
          'The team with this email already exists in the system.'
        );
      }

      // Parse email domain
      const emailParts = email.split('@');
      const emailDomain = emailParts.length === 2 ? emailParts[1] : '';

      if (!emailDomain) {
        return apiClient.invalidRequest(
          req,
          res,
          next,
          'Email domain was not parsed from the input value.'
        );
      }

      // Find clients with sign up enabled for this domain
      const clientsWithSignUpEnabled = await db
        .select()
        .from(clients)
        .where(
          and(
            eq(clients.is_sign_up_enabled, true),
            sql`LOWER(${clients.sign_up_email_domain}) LIKE '%' || LOWER(${emailDomain}) || '%'`
          )
        );

      if (!clientsWithSignUpEnabled.length) {
        return apiClient.notFound(
          req,
          res,
          next,
          'No clients with sign up email & sign up enabled were found.'
        );
      }

      // Create new team
      const newTeam = await TeamModel.create({ name, email, password });
      
      if (!newTeam) {
        throw new Error('Failed to create team');
      }

      const teamId = newTeam.team_id;

      // For each client, create related records
      await Promise.all(
        clientsWithSignUpEnabled.map(async (client) => {
          return Promise.all([
            TeamGoalsModel.create({ teamId, clientId: client.id }),
            TeamClientsModel.create({ teamId, clientId: client.id }),
            TeamMetricsModel.createTeamMetrics({ teamId, clientId: client.id }),
          ]);
        })
      );

      const userObj = {
        id: teamId,
        email: email,
        clientId: clientsWithSignUpEnabled[0].id,
      };
      const jwtToken = jwt.sign(userObj, config.jwtPassword, {
        expiresIn: '24h',
      });

      return apiClient.success(req, res, next, {
        token: jwtToken,
        user: userObj,
      });
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  refreshToken: async (req, res, next) => {
    jwt.verify(
      req.body.token,
      config.jwtPassword,
      async (err, decodedToken) => {
        if (err || !decodedToken) {
          return apiClient.invalidRequest(req, res, next);
        } else {
          try {
            const user = await findUserById(decodedToken.id);

            if (!user) {
              return apiClient.invalidRequest(req, res, next);
            }

            const userObj = {
              id: user.team_id,
              email: user.team_email,
              clientId: user.client_id,
            };
            const jwtToken = jwt.sign(userObj, config.jwtPassword, {
              expiresIn: '24h',
            });

            return apiClient.success(req, res, next, {
              token: jwtToken,
              user: userObj,
            });
          } catch (error) {
            console.error(error);
            return apiClient.serverError(req, res, next);
          }
        }
      }
    );
  },

  forgotPassword: async (req, res, next) => {
    const { email } = req.body;

    try {
      const user = await findUserByEmail(email);

      if (!user) {
        return apiClient.invalidRequest(req, res, next, 'User not found');
      }

      const token = jwt.sign({ id: user.team_id }, config.jwtPassword, {
        expiresIn: '1h',
      });

      const msg = {
        to: user.team_email,
        from: config.SENDGRID_FROM_EMAIL,
        subject: 'Password Recovery',
        text: `Please use the following token to recover your password: ${token}`,
      };

      await sgMail.send(msg);

      return apiClient.success(req, res, next, 'Password recovery email sent');
    } catch (error) {
      console.error(error, error?.response?.body?.errors);
      return apiClient.serverError(req, res, next);
    }
  },

  recoverPassword: async (req, res, next) => {
    const { teamId, token, password } = req.body;

    if (!password) {
      return apiClient.invalidRequest(
        req,
        res,
        next,
        'Password is a required argument.'
      );
    }

    try {
      jwt.verify(token, config.jwtPassword, async (err, decodedToken) => {
        if (err || !decodedToken || decodedToken.id !== teamId) {
          return apiClient.invalidRequest(req, res, next, 'Invalid token');
        }

        const user = await updateUserPassword(teamId, password);

        if (!user) {
          return apiClient.invalidRequest(req, res, next, 'User not found');
        }

        return apiClient.success(
          req,
          res,
          next,
          'Password updated successfully'
        );
      });
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  dashboard: async (req, res, next) => {
    try {
      const team = await db
        .select({
          id: lumenTeam.team_id,
          name: lumenTeam.team_name,
          email: lumenTeam.team_email,
          goal1: lumenTeam.team_goal1,
          goal2: lumenTeam.team_goal2,
          goal3: lumenTeam.team_goal3,
        })
        .from(lumenTeam)
        .where(eq(lumenTeam.team_id, req.user.id))
        .limit(1);

      if (!team.length) {
        return apiClient.notFound(req, res, next, 'Team not found');
      }

      const revenueNumbers = await db
        .select({
          id: lumenTeamRevenueNumber.number_id,
          name: lumenTeamRevenueNumber.number_name,
          companyValue: lumenTeamRevenueNumber.number_company_value,
          industryValue: lumenTeamRevenueNumber.number_industry_value,
          plannedValue: lumenTeamRevenueNumber.number_planned_value,
          fixed: lumenTeamRevenueNumber.number_fixed,
        })
        .from(lumenTeamRevenueNumber)
        .where(eq(lumenTeamRevenueNumber.number_team_id, req.user.id));

      const metrics = await db
        .select({
          id: lumenTeamMetric.metric_id,
          name: lumenTeamMetric.metric_name,
          stringValue: lumenTeamMetric.metric_string_value,
          companyValue: lumenTeamMetric.metric_company_value,
          industryValue: lumenTeamMetric.metric_industry_value,
          maximum: lumenTeamMetric.metric_maximum,
          fixed: lumenTeamMetric.metric_fixed,
        })
        .from(lumenTeamMetric)
        .where(eq(lumenTeamMetric.metric_team_id, req.user.id));

      const teamData = {
        ...team[0],
        revenueNumbers,
        metrics,
      };

      return apiClient.success(req, res, next, teamData);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  getTeamDetails: async (req, res, next) => {
    const { clientId } = req.query;
    const teamId = req.user.id;

    try {
      // Get team data
      const team = await db
        .select({
          id: lumenTeam.team_id,
          name: lumenTeam.team_name,
        })
        .from(lumenTeam)
        .where(eq(lumenTeam.team_id, teamId))
        .limit(1);

      if (!team.length) {
        return apiClient.notFound(req, res, next, 'Team not found');
      }

      // Check team-client relation
      const teamClientsRelation = await db
        .select()
        .from(teamClients)
        .where(
          and(
            eq(teamClients.team_id, teamId),
            eq(teamClients.client_id, clientId)
          )
        )
        .limit(1);

      if (!teamClientsRelation.length) {
        return apiClient.notFound(
          req,
          res,
          next,
          `Team is not related to client ${clientId}`
        );
      }

      // Get team goals
      const teamGoalsData = await db
        .select({
          goal_1: teamGoals.goal_1,
          goal_2: teamGoals.goal_2,
          goal_3: teamGoals.goal_3,
        })
        .from(teamGoals)
        .where(
          and(eq(teamGoals.team_id, teamId), eq(teamGoals.client_id, clientId))
        )
        .limit(1);

      // Get client data
      const clientData = await db
        .select({
          name: clients.name,
          backgroundImage: clients.background_image,
          logoImage: clients.logo_image,
          challengesTabName: clients.challenges_tab_name,
          homeTabName: clients.home_tab_name,
          goalsTabName: clients.goals_tab_name,
          strategicInitiativesTabName: clients.strategic_tab_name,
          orgChartTabName: clients.org_chart_tab_name,
          leaderboardTabName: clients.leaderboard_tab_name,
          selfAssessmentTabName: clients.self_assessment_tab_name,
          homeTabVisibility: clients.home_tab_visibility,
          goalsTabVisibility: clients.goals_tab_visibility,
          challengesTabVisibility: clients.challenges_tab_visibility,
          initiativesTabVisibility: clients.initiatives_tab_visibility,
          orgChartTabVisibility: clients.org_chart_tab_visibility,
          selfAssessmentTabVisibility: clients.self_assessment_tab_visibility,
          leaderboardTabVisibility: clients.leaderboard_tab_visibility,
          darkHighlightColor: clients.dark_highlight_color,
          lightHighlightColor: clients.light_highlight_color,
          decisionSchemeId: clients.decision_scheme_id, 
          decisionsTabName: clients.decision_tab_name, 
          decisionsTabVisibility: clients.decision_tab_visibility, 
          fteMax: clients.fte_max,
          investmentMax: clients.investment_max,
          aiSummaryTitle: clients.ai_summary_title,
          schemeOrder: clients.scheme_order,
        })
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      // Get self assessment answers
      const selfAssessmentAnswersData = await db
        .select({
          id: selfAssessmentAnswers.id,
        })
        .from(selfAssessmentAnswers)
        .where(
          and(
            eq(selfAssessmentAnswers.team_id, teamId),
            eq(selfAssessmentAnswers.client_id, clientId)
          )
        )
        .limit(1);

      // Get global team metrics
      const globalTeamMetricsSchemes = await db
        .select({
          id: globalTeamMetricsScheme.id,
          name: globalTeamMetricsScheme.name,
          alias: globalTeamMetricsScheme.alias,
          defaultValue: globalTeamMetricsScheme.default_value,
        })
        .from(globalTeamMetricsScheme)
        .where(eq(globalTeamMetricsScheme.client_id, clientId))
        .orderBy(globalTeamMetricsScheme.id);

      const globalTeamMetricsData = await db
        .select({
          id: globalTeamMetric.id,
          value: globalTeamMetric.value,
          schemeId: globalTeamMetric.global_team_metric_scheme_id,
        })
        .from(globalTeamMetric)
        .where(
          and(
            eq(globalTeamMetric.team_id, teamId),
            eq(globalTeamMetric.client_id, clientId)
          )
        );

      // Build the response object in the same structure as the original
      const result = {
        id: team[0].id,
        name: team[0].name || '',
        ...(teamGoalsData.length > 0 && {
          goal1: teamGoalsData[0].goal_1 || '',
          goal2: teamGoalsData[0].goal_2 || '',
          goal3: teamGoalsData[0].goal_3 || '',
        }),
        client:
          clientData.length > 0
            ? {
                ...clientData[0],
                selfAssessmentAnswersId: selfAssessmentAnswersData[0]?.id,
              }
            : {},
        globalTeamMetrics: globalTeamMetricsSchemes.map((scheme) => {
          const relatedMetric = globalTeamMetricsData.find(
            (metric) => metric.schemeId === scheme.id
          );
          return {
            id: scheme.id,
            alias: scheme.alias,
            name: scheme.name,
            defaultValue: scheme.defaultValue,
            value: relatedMetric?.value || scheme.defaultValue,
          };
        }),
      };

      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  updateTeamDetails: async (req, res, next) => {
    const teamId = req.user.id;
    const { name, goal1, goal2, goal3 } = req.body;

    try {
      const team = await updateTeamDetails(teamId, name);
      if (!team) {
        return apiClient.notFound(req, res, next, 'Team not found');
      }

      const [teamGoalsData] = await db
        .update(teamGoals)
        .set({ goal_1: goal1, goal_2: goal2, goal_3: goal3 })
        .where(eq(teamGoals.team_id, teamId))
        .returning();

      if (!teamGoalsData) {
        return apiClient.notFound(req, res, next, 'Team goals not found');
      }

      return apiClient.success(
        req,
        res,
        next,
        'Team details updated successfully'
      );
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  getInitiatives: async (req, res, next) => {
    const { clientId } = req.query;
    const teamId = req.user.id;

    try {
      const client = await db
        .select()
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      if (!client.length) {
        return apiClient.notFound(req, res, next, 'Client not found');
      }

      const schemeId = client[0].initiative_scheme_id;

      const initiativeScheme = await db
        .select({
          id: lumenInitiativeScheme.scheme_id,
          name: lumenInitiativeScheme.scheme_name,
          initiativeNumber: lumenInitiativeScheme.scheme_initiative_number,
        })
        .from(lumenInitiativeScheme)
        .where(
          and(
            eq(lumenInitiativeScheme.scheme_id, schemeId),
            eq(lumenInitiativeScheme.scheme_disabled, false)
          )
        )
        .limit(1);

      if (!initiativeScheme.length) {
        return apiClient.notFound(
          req,
          res,
          next,
          'Initiative scheme not found'
        );
      }

      const initiatives = await db
        .select({
          id: lumenInitiative.initiative_id,
          name: lumenInitiative.initiative_name,
          description: lumenInitiative.initiative_description,
          metric1: lumenInitiative.metric1,
          metric2: lumenInitiative.metric2,
          metric3: lumenInitiative.metric3,
        })
        .from(lumenInitiative)
        .where(eq(lumenInitiative.initiative_scheme_id, schemeId));

      const selectedInitiatives = await db
        .select({
          id: lumenTeamSelectedInitiative.selected_initiative_id,
        })
        .from(lumenTeamSelectedInitiative)
        .where(
          and(
            eq(lumenTeamSelectedInitiative.selected_team_id, teamId),
            eq(lumenTeamSelectedInitiative.client_id, clientId)
          )
        );

      const result = {
        client: client[0],
        initiativeScheme: initiativeScheme[0],
        initiatives,
        selectedInitiatives,
      };

      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  saveInitiatives: async (req, res, next) => {
    try {
      const teamId = req.user.id;
      const { clientId, initiatives } = req.body;

      // Get global team metrics and schemes
      const [globalTeamMetricsData, globalTeamMetricsSchemesData] =
        await Promise.all([
          db
            .select()
            .from(globalTeamMetric)
            .where(
              and(
                eq(globalTeamMetric.team_id, teamId),
                eq(globalTeamMetric.client_id, clientId)
              )
            ),
          db
            .select()
            .from(globalTeamMetricsScheme)
            .where(eq(globalTeamMetricsScheme.client_id, clientId)),
        ]);

      // Map team metrics similar to original implementation
      let teamMetrics = globalTeamMetricsSchemesData.map((scheme) => {
        const relatedMetric = globalTeamMetricsData.find(
          (metric) => metric.global_team_metric_scheme_id === scheme.id
        );

        return {
          ...scheme,
          globalTeamMetricId: relatedMetric.id,
          value: relatedMetric.value,
        };
      });

      // Process each initiative
      for (const initiative of initiatives) {
        const [existingInitiative, existingSelectedInitiative] =
          await Promise.all([
            db
              .select()
              .from(lumenInitiative)
              .where(eq(lumenInitiative.initiative_id, initiative.id))
              .limit(1),
            db
              .select()
              .from(lumenTeamSelectedInitiative)
              .where(
                and(
                  eq(
                    lumenTeamSelectedInitiative.selected_initiative_id,
                    initiative.id
                  ),
                  eq(lumenTeamSelectedInitiative.selected_team_id, teamId),
                  eq(lumenTeamSelectedInitiative.client_id, clientId)
                )
              )
              .limit(1),
          ]);

        const initialInitiative = existingInitiative[0];
        const initialSelectedInitiative = existingSelectedInitiative[0];

        if (initiative.selected && !initialSelectedInitiative) {
          // Update team metrics for new selection
          teamMetrics = modifyGlobalTeamMetrics(
            teamMetrics,
            initialInitiative,
            true
          );

          // Insert new selected initiative
          await db.insert(lumenTeamSelectedInitiative).values({
            selected_initiative_id: initiative.id,
            selected_team_id: teamId,
            client_id: clientId,
          });
        } else if (!initiative.selected && initialSelectedInitiative) {
          // Update team metrics for deselection
          teamMetrics = modifyGlobalTeamMetrics(teamMetrics, initialInitiative);

          // Delete selected initiative
          await db
            .delete(lumenTeamSelectedInitiative)
            .where(
              and(
                eq(
                  lumenTeamSelectedInitiative.selected_initiative_id,
                  initiative.id
                ),
                eq(lumenTeamSelectedInitiative.selected_team_id, teamId),
                eq(lumenTeamSelectedInitiative.client_id, clientId)
              )
            );
        }
      }

      // Update global team metrics
      for (const metric of teamMetrics) {
        await db
          .update(globalTeamMetric)
          .set({
            value: metric.value,
          })
          .where(eq(globalTeamMetric.id, metric.globalTeamMetricId));
      }

      return apiClient.success(req, res, next);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  getChallenges: async (req, res, next) => {
    const { clientId } = req.query;
    const teamId = req.user.id;
    const result = {};

    try {
      // Get client data
      const clientData = await db
        .select()
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      if (!clientData.length) {
        return apiClient.notFound(req, res, next);
      }

      const challengeSchemeId = clientData[0].challenge_scheme_id;
      result.challengeSchemeId = challengeSchemeId;

      // Get challenges scheme, challenges and selected challenges in parallel
      const [challengesScheme, challenges, selectedChallenges] =
        await Promise.all([
          db
            .select({
              id: lumenChallengeScheme.scheme_id,
              name: lumenChallengeScheme.scheme_name,
            })
            .from(lumenChallengeScheme)
            .where(
              and(
                eq(lumenChallengeScheme.scheme_id, challengeSchemeId),
                eq(lumenChallengeScheme.scheme_disabled, false)
              )
            ),
          db
            .select({
              id: lumenChallenge.challenge_id,
              imageUrl: lumenChallenge.challenge_image_url,
              description: lumenChallenge.challenge_description,
              bubbleSize: lumenChallenge.challenge_bubble_size,
              optionA: lumenChallenge.challenge_option_a,
              consequenceA: lumenChallenge.challenge_consequence_a,
              optionMetric1A: lumenChallenge.challenge_option_metric1_a,
              optionMetric2A: lumenChallenge.challenge_option_metric2_a,
              optionMetric3A: lumenChallenge.challenge_option_metric3_a,
              optionB: lumenChallenge.challenge_option_b,
              consequenceB: lumenChallenge.challenge_consequence_b,
              optionMetric1B: lumenChallenge.challenge_option_metric1_b,
              optionMetric2B: lumenChallenge.challenge_option_metric2_b,
              optionMetric3B: lumenChallenge.challenge_option_metric3_b,
              optionC: lumenChallenge.challenge_option_c,
              consequenceC: lumenChallenge.challenge_consequence_c,
              optionMetric1C: lumenChallenge.challenge_option_metric1_c,
              optionMetric2C: lumenChallenge.challenge_option_metric2_c,
              optionMetric3C: lumenChallenge.challenge_option_metric3_c,
              // Alt challenge flags
              optionAIsAlt: lumenChallenge.option_a_is_alt,
              optionBIsAlt: lumenChallenge.option_b_is_alt,
              optionCIsAlt: lumenChallenge.option_c_is_alt,
              isAltChallenge: lumenChallenge.is_alt_challenge,
              altOfChallenge: lumenChallenge.alt_of_challenge,
              // Group information
              groupId: lumenChallenge.group_id,
              groupName: challengeGroups.name,
            })
            .from(lumenChallenge)
            .leftJoin(challengeGroups, eq(lumenChallenge.group_id, challengeGroups.id))
            .where(eq(lumenChallenge.challenge_scheme_id, challengeSchemeId)),
          db
            .select({
              id: lumenTeamSelectedChallenge.selected_challenge_id,
              selectedA: lumenTeamSelectedChallenge.selected_option_a,
              selectedB: lumenTeamSelectedChallenge.selected_option_b,
              selectedC: lumenTeamSelectedChallenge.selected_option_c,
            })
            .from(lumenTeamSelectedChallenge)
            .where(
              and(
                eq(lumenTeamSelectedChallenge.selected_team_id, teamId),
                eq(lumenTeamSelectedChallenge.client_id, clientId)
              )
            ),
        ]);

      // Set scheme data if exists
      if (challengesScheme.length) {
        const { id, name } = challengesScheme[0];
        result.scheme = {
          id,
          name,
        };
      }

      // Map challenges with selected status
      result.scheme.challenges = challenges.map((challenge) => {
        const relatedSelectedChallenge = selectedChallenges.find(
          (selected) => selected.id === challenge.id
        );

        const extraProperties = relatedSelectedChallenge
          ? {
              selectedA: relatedSelectedChallenge.selectedA,
              selectedB: relatedSelectedChallenge.selectedB,
              selectedC: relatedSelectedChallenge.selectedC,
            }
          : {};

        return {
          ...challenge,
          ...extraProperties,
        };
      });

      return apiClient.success(req, res, next, result.scheme);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  getWelcomePage: async (req, res, next) => {
    const { clientId } = req.query;

    try {
      const clientData = await db
        .select()
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      if (!clientData.length) {
        return apiClient.notFound(req, res, next, 'Client not found');
      }

      const welcomePageData = await db
        .select()
        .from(welcomePage)
        .where(eq(welcomePage.id, clientData[0].home_scheme_id))
        .limit(1);

      if (!welcomePageData.length) {
        return apiClient.notFound(req, res, next, 'No welcome page data found');
      }

      return apiClient.success(req, res, next, welcomePageData[0]);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  saveChallenges: async (req, res, next) => {
    const teamId = req.user.id;
    // Get clientId from request body instead of user token
    const { clientId, challenges } = req.body;

    try {
      console.log('Saving challenges for team:', teamId, 'client:', clientId);
      
      await db.transaction(async (tx) => {
        // Delete existing selected challenges
        console.log('Deleting existing selected challenges');
        await tx
          .delete(lumenTeamSelectedChallenge)
          .where(
            and(
              eq(lumenTeamSelectedChallenge.selected_team_id, teamId),
              eq(lumenTeamSelectedChallenge.client_id, clientId)
            )
          );

        // Get global team metrics data
        const [globalTeamMetricsData, globalTeamMetricsSchemesData] = await Promise.all([
          tx
            .select()
            .from(globalTeamMetric)
            .where(
              and(
                eq(globalTeamMetric.team_id, teamId),
                eq(globalTeamMetric.client_id, clientId)
              )
            ),
          tx
            .select()
            .from(globalTeamMetricsScheme)
            .where(eq(globalTeamMetricsScheme.client_id, clientId))
        ]);

        // Map team metrics
        let teamMetrics = globalTeamMetricsSchemesData.map(scheme => {
          const relatedMetric = globalTeamMetricsData.find(
            metric => metric.global_team_metric_scheme_id === scheme.id
          );
          return {
            ...scheme,
            globalTeamMetricId: relatedMetric?.id,
            value: relatedMetric?.value || scheme.default_value
          };
        });

        console.log('Processing challenges:', challenges.length);
        
        // Process each challenge
        for (const challenge of challenges) {
          const { id, selectedA, selectedB, selectedC } = challenge;
          
          // Skip if no option selected
          if (!selectedA && !selectedB && !selectedC) {
            continue;
          }

          console.log('Saving challenge selection:', {id, selectedA, selectedB, selectedC});

          // Get current challenge data for metrics calculation
          const [currentChallenge] = await tx
            .select()
            .from(lumenChallenge)
            .where(eq(lumenChallenge.challenge_id, id))
            .limit(1);

          if (!currentChallenge) {
            console.warn('Challenge not found:', id);
            continue;
          }

          // Insert selected challenge
          await tx
            .insert(lumenTeamSelectedChallenge)
            .values({
              selected_challenge_id: id,
              selected_team_id: teamId,
              selected_option_a: selectedA || false,
              selected_option_b: selectedB || false,
              selected_option_c: selectedC || false,
              client_id: clientId,
            });

          // Update metrics based on selection
          if (selectedA) {
            teamMetrics = updateTeamMetrics(teamMetrics, currentChallenge, 'a');
          } else if (selectedB) {
            teamMetrics = updateTeamMetrics(teamMetrics, currentChallenge, 'b');
          } else if (selectedC) {
            teamMetrics = updateTeamMetrics(teamMetrics, currentChallenge, 'c');
          }
        }

        // Update global team metrics
        console.log('Updating global team metrics');
        for (const metric of teamMetrics) {
          if (metric.globalTeamMetricId) {
            await tx
              .update(globalTeamMetric)
              .set({
                value: metric.value
              })
              .where(eq(globalTeamMetric.id, metric.globalTeamMetricId));
          }
        }
      });

      console.log('Challenges saved successfully');
      return apiClient.success(req, res, next);
    } catch (error) {
      console.error('Failed to save challenges:', error);
      return apiClient.serverError(req, res, next, error.message);
    }
  },

  getLeaderboard: async (req, res, next) => {
    const { clientId } = req.query;
    const result = {};

    try {
      // Get client data
      const clientData = await db
        .select()
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      if (!clientData.length) {
        return apiClient.notFound(req, res, next, 'Client not found');
      }

      const leaderboardSchemeId = clientData[0].leaderboard_scheme_id;

      // Get leaderboard scheme
      const leaderboardSchemeData = await db
        .select()
        .from(leaderboardScheme)
        .where(eq(leaderboardScheme.id, leaderboardSchemeId))
        .limit(1);

      if (!leaderboardSchemeData.length) {
        return apiClient.notFound(
          req,
          res,
          next,
          'Leaderboard Scheme not found'
        );
      }

      // Set scheme data in result
      result.scheme = {
        id: leaderboardSchemeId,
        name: leaderboardSchemeData[0].name,
      };

      // Get leaderboard regions
      const leaderboardRegions = await db
        .select()
        .from(leaderboardRegion)
        .where(
          eq(leaderboardRegion.leaderboard_scheme_id, leaderboardSchemeId)
        );

      const regionIds = leaderboardRegions.map((region) => region.id);

      // Get leaderboard users
      const leaderboardUsers = await db
        .select()
        .from(leaderboardUser)
        .where(inArray(leaderboardUser.leaderboard_region_id, regionIds))
        .orderBy(leaderboardUser.points, 'desc');

      // Map users to their regions
      const regionsWithUsers = leaderboardRegions.map((region) => ({
        ...region,
        users: leaderboardUsers.filter(
          (user) => user.leaderboard_region_id === region.id
        ),
      }));

      // Add regions to scheme
      result.scheme.regions = regionsWithUsers;

      return apiClient.success(req, res, next, result.scheme);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  getOrgChart: async (req, res, next) => {
    const teamId = req.user.id;
    const { clientId } = req.query;
    const result = {};

    try {
      // Get client data
      const clientData = await db
        .select()
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      if (!clientData.length) {
        return apiClient.notFound(req, res, next, 'Client not found');
      }

      const orgChartSchemeId = clientData[0].org_chart_scheme_id;

      // Get org chart scheme
      const orgChartSchemeData = await db
        .select()
        .from(orgChartScheme)
        .where(eq(orgChartScheme.id, orgChartSchemeId))
        .limit(1);

      if (!orgChartSchemeData.length) {
        return apiClient.notFound(req, res, next, 'Org Chart Scheme not found');
      }

      // Set initial scheme data
      result.scheme = {
        id: orgChartSchemeId,
        name: orgChartSchemeData[0].name,
        meetingPoints: 300,
      };

      // Get org charts
      const orgCharts = await db
        .select()
        .from(orgChart)
        .where(eq(orgChart.org_chart_scheme_id, orgChartSchemeId));

      // Get org chart types for all charts
      const orgChartTypes = await db
        .select()
        .from(orgChartType)
        .where(
          inArray(
            orgChartType.org_chart_id,
            orgCharts.map((chart) => chart.id)
          )
        );

      // Get org chart users for all types
      const orgChartUsers = await db
        .select()
        .from(orgChartUser)
        .where(
          inArray(
            orgChartUser.org_chart_type_id,
            orgChartTypes.map((type) => type.id)
          )
        );

      // Get team selected org charts
      const teamSelectedOrgCharts = await db
        .select()
        .from(teamSelectedOrgChart)
        .where(
          and(
            eq(teamSelectedOrgChart.team_id, teamId),
            eq(teamSelectedOrgChart.client_id, clientId)
          )
        );

      // Process org chart types and their users
      for (const orgChartType of orgChartTypes) {
        orgChartType.users = orgChartUsers
          .filter((user) => user.org_chart_type_id === orgChartType.id)
          .map((user) => {
            const relatedSelectedUser = teamSelectedOrgCharts.find(
              (selected) => selected.org_chart_user_id === user.id
            );

            if (!relatedSelectedUser) return user;

            const {
              meet_1,
              meet_2,
              meet_3,
              meet_1_time,
              meet_2_time,
              meet_3_time,
            } = relatedSelectedUser;

            let status = user.status;

            // Update status and meeting points
            if (meet_1) {
              status++;
              result.scheme.meetingPoints -= parseInt(user.meet_1_points, 10);
            }

            if (meet_2) {
              status++;
              result.scheme.meetingPoints -= parseInt(user.meet_2_points, 10);
            }

            if (meet_3) {
              status++;
              result.scheme.meetingPoints -= parseInt(user.meet_3_points, 10);
            }

            return {
              ...user,
              status,
              meet_1,
              meet_2,
              meet_3,
              meet_1_time,
              meet_2_time,
              meet_3_time,
            };
          });
      }

      // Add types to their respective charts
      for (const chart of orgCharts) {
        chart.orgChartTypes = orgChartTypes.filter(
          (type) => type.org_chart_id === chart.id
        );
      }

      result.scheme.orgCharts = orgCharts;

      return apiClient.success(req, res, next, result.scheme);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  saveOrgChart: async (req, res, next) => {
    const teamId = req.user.id;
    const { clientId, orgCharts } = req.body;

    try {
      await db.transaction(async (tx) => {
        // Check if teamSelectedOrgChart exists before attempting to delete
        const existingOrgCharts = await tx
          .select()
          .from(teamSelectedOrgChart)
          .where(
            and(
              eq(teamSelectedOrgChart.team_id, teamId),
              eq(teamSelectedOrgChart.client_id, clientId)
            )
          );

        if (existingOrgCharts.length > 0) {
          // Only delete if there are existing entries
          await tx
            .delete(teamSelectedOrgChart)
            .where(
              and(
                eq(teamSelectedOrgChart.team_id, teamId),
                eq(teamSelectedOrgChart.client_id, clientId)
              )
            );
        }

        // Process each org chart and its nested structure
        for (const orgChart of orgCharts) {
          const { orgChartTypes } = orgChart;

          for (const orgChartType of orgChartTypes) {
            const { users } = orgChartType;

            for (const user of users) {
              const {
                id,
                meet_1,
                meet_2,
                meet_3,
                meet_1_time,
                meet_2_time,
                meet_3_time,
              } = user;

              // Skip if no meetings are selected
              if (!meet_1 && !meet_2 && !meet_3) continue;

              // Convert date strings to Date objects if they exist
              const meet1Time = meet_1_time ? new Date(meet_1_time) : null;
              const meet2Time = meet_2_time ? new Date(meet_2_time) : null;
              const meet3Time = meet_3_time ? new Date(meet_3_time) : null;

              // Insert new team selected org chart
              await tx.insert(teamSelectedOrgChart).values({
                org_chart_user_id: id,
                team_id: teamId,
                client_id: clientId,
                meet_1: meet_1 || false,
                meet_2: meet_2 || false,
                meet_3: meet_3 || false,
                meet_1_time: meet1Time,
                meet_2_time: meet2Time,
                meet_3_time: meet3Time,
                created_at: new Date(),
              });
            }
          }
        }
      });

      return apiClient.success(req, res, next, 'OK');
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  getAllAvailableWorkshops: async (req, res, next) => {
    try {
      const teamId = req.user.id;

      const workshops = await db
        .select()
        .from(teamClients)
        .leftJoin(clients, eq(teamClients.client_id, clients.id))
        .where(eq(teamClients.team_id, teamId));

      if (!workshops.length) {
        console.log(`Team ${teamId} doesn't have any related client.`);
        return apiClient.notFound(req, res, next, 'No related clients found');
      }

      return apiClient.success(req, res, next, workshops);
    } catch (error) {
      console.error(error);
      return apiClient.serverError(req, res, next);
    }
  },

  getDecision: async (req, res, next) => {
    const { clientId } = req.query;
    const teamId = req.user.id;

    try {
      // Get client data
      const clientData = await db
        .select()
        .from(clients)
        .where(eq(clients.id, clientId))
        .limit(1);

      if (!clientData.length) {
        return apiClient.notFound(req, res, next, 'Client not found');
      }

      const decisionSchemeId = clientData[0].decision_scheme_id;

      // Get the decision scheme
      const scheme = await findDecisionSchemeById(decisionSchemeId);

      if (!scheme) {
        return apiClient.notFound(req, res, next, 'Decision scheme not found');
      }

      // Get all pages for this decision
      const pages = await findDecisionPagesBySchemeId(scheme.id);

      const result = {
        id: scheme.id,
        name: scheme.name,
        pnl: scheme.pnl || null,
        created_at: scheme.created_at,
        updated_at: scheme.updated_at,
        is_disabled: scheme.disabled || false,
        pages: (pages || []).map((page) => ({
          id: page.id,
          page_number: page.page_number,
          sliders: page.sliders || [],
          incentives: page.incentives || [],
          page_pnl: page.page_pnl,
          page_image: page.page_image,
          page_name: page.page_name,
        })),
      };

      return apiClient.success(req, res, next, result);
    } catch (error) {
      console.error('Error in get decision:', error);
      return apiClient.serverError(req, res, next);
    }
  },
};

/*
 * Modify global team metrics after selecting initiative
 */
function modifyGlobalTeamMetrics(
  globalTeamMetrics = [],
  initialInitiative = {},
  isSelected = false
) {
  return globalTeamMetrics.map((globalTeamMetric) => {
    const { alias, value } = globalTeamMetric;
    const [name, index] = alias.split(' ');
    const key = `${name}${index}`;
    const newValue = initialInitiative[key];

    return {
      ...globalTeamMetric,
      value: isSelected ? value + newValue : value - newValue,
    };
  });
}

/**
 * Converts snake_case to camelCase
 * @param snakeCaseString
 * @returns {*}
 */
function convertSnakeToCamelCase(snakeCaseString) {
  return snakeCaseString.replace(/([-_]\w)/g, (g) => g[1].toUpperCase());
}

// Helper function to update team metrics
function updateTeamMetrics(metrics, challenge, option) {
  return metrics.map((metric, index) => {
    const metricKey = `challenge_option_metric${index + 1}_${option}`;
    const metricValue = challenge[metricKey] || 0;
    
    return {
      ...metric,
      value: (metric.value || 0) + metricValue
    };
  });
}

export default USER_CONTROLLER;
