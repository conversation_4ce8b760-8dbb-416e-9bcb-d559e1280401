import React, { useEffect, useState } from 'react';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';
import { useNavigate } from 'react-router-dom';
import { Loader } from 'lucide-react';
import { toast } from 'sonner';
import to from 'await-to-js';
import { getWelcomePage } from '../../actions/user';
import styles from './welcome-page.module.scss';

const WelcomePage = ({ user: { client = {} } }) => {
  const navigate = useNavigate();
  const [isLoading, setLoading] = useState(false);
  const [welcomePage, setWelcomePage] = useState({});
  const { theme } = useTheme();
  const { homeTabName, homeTabVisibility } = client;

  useEffect(() => {
    if (client?.hasOwnProperty('homeTabVisibility') && !homeTabVisibility) {
      navigate('/goals');
      return;
    }
    proceedWelcomePage();
  }, [client, homeTabVisibility, navigate]);

  async function proceedWelcomePage() {
    setLoading(true);
    const [err, res] = await to(getWelcomePage().payload);

    if (err) {
      setLoading(false);
      return toast.error('Something went wrong.');
    }

    setWelcomePage(res.data);
    setLoading(false);
  }

  if (isLoading) return <Loader />;

  return (
    <section className={styles.container}>
      {/* Hero Image Section */}
      {welcomePage.image && (
        <div className={styles.heroSection}>
          <img src={welcomePage.image} alt="Welcome" className={styles.heroImage} />
          <div className={styles.heroOverlay} />
        </div>
      )}

      {/* Content Section */}
      <div className={styles.contentSection}>
        <div className={`${styles.proseContainer} ${theme === 'dark' ? styles.dark : ''}`}>
          {/* <p className="font-semibold mt-4 text-lg text-white">
            {homeTabName || "Welcome"}
          </p> */}
          {welcomePage?.text && (
            <div
              className={styles.textContent}
              dangerouslySetInnerHTML={{ __html: welcomePage.text }}
            />
          )}
        </div>

        {/* Cards Section with enhanced glass effect */}
        {welcomePage.cards && (
          <div className={styles.cardsGrid}>
            {welcomePage.cards.map((card, index) => (
              <div
                key={index}
                className={styles.card}
              >
                {card.icon && <div className={styles.cardIcon}>{card.icon}</div>}
                <h3 className={styles.cardTitle}>{card.title}</h3>
                <p className={styles.cardContent}>{card.content}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default WelcomePage;
