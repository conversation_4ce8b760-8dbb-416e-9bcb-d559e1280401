// WelcomePage component styles
.container {
  // Base container styles
}

.heroSection {
  position: relative;
  height: 18rem; // h-72
  width: 100%;
  margin-bottom: 2rem; // mb-8
  
  @media (min-width: 640px) {
    height: 24rem; // sm:h-96
  }
}

.heroImage {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.heroOverlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
}

.contentSection {
  padding: 2rem; // p-8
}

.proseContainer {
  max-width: none;
  
  &.dark {
    // prose-invert styles would go here if needed
    color: #e5e7eb;
  }
}

.textContent {
  font-size: 1rem; // text-base
  line-height: 1.375; // leading-snug
}

.cardsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem; // gap-6
  margin-top: 3rem; // mt-12
  
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr); // sm:grid-cols-2
  }
}

.card {
  backdrop-filter: blur(24px); // backdrop-blur-xl
  padding: 2rem; // p-8
  border-radius: 0.75rem; // rounded-xl
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); // shadow-lg
  transform: scale(1);
  transition: all 0.3s ease; // transition-all duration-300
  
  &:hover {
    transform: scale(1.02); // hover:scale-102
  }
}

.cardIcon {
  color: #3b82f6; // text-blue-500
  margin-bottom: 1rem; // mb-4
  font-size: 1.875rem; // text-3xl
}

.cardTitle {
  font-size: 1.5rem; // text-2xl
  font-weight: 600; // font-semibold
  margin-bottom: 1rem; // mb-4
}

.cardContent {
  font-size: 1.125rem; // text-lg
  line-height: 1.625; // leading-relaxed
}

// Dark mode styles
:global(.dark) {
  .textContent {
    color: #9ca3af; // text-gray-400
  }
  
  .card {
    background-color: rgba(255, 255, 255, 0.05); // bg-white/5
    border: 1px solid rgba(255, 255, 255, 0.1); // border-white/10
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1); // hover:bg-white/10
    }
  }
  
  .cardTitle {
    color: #ffffff; // text-white
  }
  
  .cardContent {
    color: #d1d5db; // text-gray-300
  }
}

// Light mode styles
:global(.light) {
  .textContent {
    color: #1f2937; // text-gray-800
  }
  
  .card {
    background-color: rgba(255, 255, 255, 0.6); // bg-white/60
    border: 1px solid rgba(156, 163, 175, 0.3); // border-gray-300/30
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.8); // hover:bg-white/80
    }
  }
  
  .cardTitle {
    color: #1f2937; // text-gray-800
  }
  
  .cardContent {
    color: #1f2937; // text-gray-800
  }
}
