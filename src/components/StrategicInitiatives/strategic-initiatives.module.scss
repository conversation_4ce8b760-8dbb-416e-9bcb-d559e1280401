.container {
  display: flex;
  flex-direction: column;
  padding: 1.5rem 1rem;
  margin-bottom: 1.5rem;
  
  @media (min-width: 768px) {
    flex-direction: row;
    gap: 1.5rem;
  }
}

.sidebarContainer {
  @media (min-width: 768px) {
    width: 25%;
  }
}

.mainContainer {
  @media (min-width: 768px) {
    width: 75%;
  }
}

.contentWrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.saveButton {
  font-weight: 600;
  transition-property: opacity;
  transition-duration: 150ms;
  
  &:hover {
    opacity: 0.9;
  }
}

.loaderIcon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.initiativesContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: transparent;
  backdrop-filter: blur(24px);
  border-radius: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
}

.initiativeItem {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  &:last-child {
    border-bottom: 0;
    padding-bottom: 0;
  }
}

.checkbox {
  margin-top: 0.375rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.initiativeContent {
  flex: 1;
}

.initiativeTitle {
  display: block;
  font-size: 1rem;
  font-weight: 500;
  color: white;
  cursor: pointer;
}

.initiativeDescription {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: rgb(156, 163, 175);
  line-height: 1.5;
}

.metricsTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  padding-left: 1rem;
}