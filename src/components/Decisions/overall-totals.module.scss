
.container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
  
  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.card {
  padding: 1.5rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(24px);
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
  }
}

.cardContent {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.iconContainer {
  padding: 0.75rem;
  border-radius: 50%;
}

.icon {
  color: #60a5fa;
  
  &.emerald {
    color: #34d399;
  }
}

.textContainer {
  flex: 1;
}

.label {
  font-size: 0.875rem;
  color: #6b7280;
}

.value {
  font-size: 1.5rem;
  font-weight: 600;
}

.indigo {
  .iconContainer {
    background-color: #e0e7ff;
  }
  
  .value {
    color: #3730a3;
  }
}

.emerald {
  .iconContainer {
    background-color: #d1fae5;
  }
  
  .value {
    color: #047857;
  }
}

// Dark mode styles
:global(.dark) {
  .card {
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    &.indigo {
      background-color: rgba(99, 102, 241, 0.1);
    }
    
    &.emerald {
      background-color: rgba(16, 185, 129, 0.1);
    }
  }
  
  .label {
    color: #9ca3af;
  }
  
  .indigo {
    .iconContainer {
      background-color: rgba(99, 102, 241, 0.2);
    }
    
    .value {
      color: #60a5fa;
    }
  }
  
  .emerald {
    .iconContainer {
      background-color: rgba(16, 185, 129, 0.2);
    }
    
    .value {
      color: #34d399;
    }
  }
}
