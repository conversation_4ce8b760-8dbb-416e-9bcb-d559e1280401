import React, { useState } from 'react';
import { Row, Col, FormGroup, ControlLabel, Button, ListGroup, ListGroupItem } from 'react-bootstrap';
import { formatCurrency } from '../../../utils/formatters';

export default function IncentivesList({
  listNumber,
  incentives = [],
  optionLimit = 1,
  onOptionLimitChange,
  onChange,
}) {
  const [newIncentive, setNewIncentive] = useState({ label: '', fte: '', investment: '' });

  // Ensure incentives is always an array
  const safeIncentives = Array.isArray(incentives) ? incentives : [];

  const handleAddIncentive = () => {
    if (newIncentive.label.trim()) {
      const updatedIncentives = [
        ...safeIncentives,
        {
          label: newIncentive.label.trim(),
          fte: Number(newIncentive.fte) || 0,
          investment: Number(newIncentive.investment.replace(/[^0-9.-]+/g, '')) || 0, // Remove currency formatting before saving
        },
      ];
      onChange(updatedIncentives);
      setNewIncentive({ label: '', fte: '', investment: '' });
    }
  };

  const handleRemoveIncentive = (index) => {
    const updatedIncentives = safeIncentives.filter((_, i) => i !== index);
    onChange(updatedIncentives);

    // Adjust optionLimit if it's greater than the new list length
    if (optionLimit > updatedIncentives.length) {
      onOptionLimitChange(Math.max(1, updatedIncentives.length));
    }
  };

  const handleOptionLimitChange = (e) => {
    const value = parseInt(e.target.value, 10);
    if (!isNaN(value)) {
      // Ensure value is between 1 and incentives.length
      const newValue = Math.min(Math.max(1, value), safeIncentives.length);
      onOptionLimitChange(newValue);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddIncentive();
    }
  };

  const handleInvestmentChange = (value) => {
    // Remove any non-numeric characters except decimal point
    const numericValue = value.replace(/[^0-9.-]+/g, '');
    setNewIncentive((prev) => ({ ...prev, investment: numericValue }));
  };

  return (
    <div
      style={{
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '15px',
        marginBottom: '15px',
      }}
    >
      <Row>
        <Col md={12}>
          <FormGroup>
            <ControlLabel>Initiatives</ControlLabel>

            {/* Option Limit Input */}
            <Row style={{ marginBottom: '15px' }}>
              <Col md={4}>
                <FormGroup>
                  <ControlLabel>Number of selectable options</ControlLabel>
                  <input
                    type="number"
                    className="form-control"
                    value={optionLimit}
                    onChange={handleOptionLimitChange}
                    min={1}
                    max={safeIncentives.length || 1}
                    disabled={safeIncentives.length === 0}
                  />
                  <small className="text-muted">
                    Select between 1 and {Math.max(1, safeIncentives.length)} options
                  </small>
                </FormGroup>
              </Col>
            </Row>

            <div style={{ marginBottom: '10px' }}>
              <Row>
                <Col md={4}>
                  <input
                    type="text"
                    className="form-control"
                    value={newIncentive.label}
                    onChange={(e) => setNewIncentive((prev) => ({ ...prev, label: e.target.value }))}
                    onKeyPress={handleKeyPress}
                    placeholder="Enter Initiatives label"
                    style={{ marginBottom: '10px' }}
                  />
                </Col>
                <Col md={3}>
                  <input
                    type="number"
                    className="form-control"
                    value={newIncentive.fte}
                    onChange={(e) => setNewIncentive((prev) => ({ ...prev, fte: e.target.value }))}
                    placeholder="Enter FTE"
                    style={{ marginBottom: '10px' }}
                  />
                </Col>
                <Col md={3}>
                  <input
                    type="text"
                    className="form-control"
                    value={newIncentive.investment ? formatCurrency(newIncentive.investment) : ''}
                    onChange={(e) => handleInvestmentChange(e.target.value)}
                    placeholder="Enter investment"
                    style={{ marginBottom: '10px' }}
                  />
                </Col>
                <Col md={2}>
                  <Button
                    onClick={handleAddIncentive}
                    disabled={!newIncentive.label.trim()}
                  >
                    Add
                  </Button>
                </Col>
              </Row>
            </div>

            <ListGroup>
              {safeIncentives.map((incentive, index) => (
                <ListGroupItem
                  key={index}
                  style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <div style={{ flex: 1 }}>
                    <strong>{incentive.label}</strong>
                    <div style={{ fontSize: '0.9em', color: '#666' }}>
                      FTE: {incentive.fte} | Investment: {formatCurrency(incentive.investment)}
                    </div>
                  </div>
                  <Button
                    bsStyle="link"
                    onClick={() => handleRemoveIncentive(index)}
                    style={{ padding: '0', color: '#d9534f' }}
                  >
                    <i className="glyphicon glyphicon-remove" />
                  </Button>
                </ListGroupItem>
              ))}
            </ListGroup>

            {/* {safeIncentives.length >= 10 && <small className="text-muted">Maximum 10 incentives allowed</small>} */}
          </FormGroup>
        </Col>
      </Row>
    </div>
  );
}
