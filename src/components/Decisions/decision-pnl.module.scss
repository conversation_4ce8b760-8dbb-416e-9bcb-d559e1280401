// DecisionPNL component styles
.container {
  padding: 2rem; // p-8
  border-radius: 0.5rem; // rounded-lg
}

.title {
  font-size: 1.5rem; // text-2xl
  font-weight: 600; // font-semibold
  margin-bottom: 1.5rem; // mb-6
}

.noDataText {
  font-size: 1.125rem; // text-lg
  color: #4b5563; // text-gray-600
}

.summarySection {
  margin-bottom: 1.5rem; // mb-6
  padding: 1.5rem; // p-6
  border-radius: 0.5rem; // rounded-lg
}

.summaryTitle {
  font-size: 1.25rem; // text-xl
  font-weight: 500; // font-medium
  margin-bottom: 0.75rem; // mb-3
}

.summaryContent {
  display: flex;
  flex-direction: column;
  gap: 1rem; // space-y-4
}

.analysisButton {
  margin-top: 1rem; // mt-4
  padding: 0.5rem 1rem; // px-4 py-2
  background-color: #2563eb; // bg-blue-600
  border-radius: 0.375rem; // rounded-md
  color: white;
  transition: all 0.3s ease; // transition-colors
  border: none;
  cursor: pointer;
  
  &:hover:not(:disabled) {
    background-color: #1d4ed8; // hover:bg-blue-700
  }
  
  &:disabled {
    background-color: rgba(37, 99, 235, 0.5); // disabled:bg-blue-600/50
    cursor: not-allowed; // disabled:cursor-not-allowed
  }
}

.impactOverview {
  margin-top: 1rem; // mt-4
}

.impactTitle {
  font-size: 1.125rem; // text-lg
  font-weight: 500; // font-medium
  margin-bottom: 0.5rem; // mb-2
}

.impactList {
  list-style-type: disc; // list-disc
  list-style-position: inside; // list-inside
  display: flex;
  flex-direction: column;
  gap: 0.5rem; // space-y-2
  margin-left: 1rem; // ml-4
}

.tableContainer {
  margin-top: 2rem; // mt-8
}

.table {
  width: 100%; // w-full
  margin-bottom: 2rem; // mb-8
}

.tableHeader {
  // Table header styling
}

.tableHeaderCell {
  padding: 0.75rem 1rem; // py-3 px-4
  text-align: left;
  font-weight: 600; // font-semibold
  
  &.textRight {
    text-align: right;
  }
}

.tableRow {
  border-top: 1px solid;
  transition: background-color 0.3s ease;
  
  &:hover {
    // Hover styles applied via theme classes
  }
}

.tableCell {
  padding: 0.75rem 1rem; // py-3 px-4
  
  &.textLeft {
    text-align: left;
  }
  
  &.textRight {
    text-align: right;
  }
  
  &.positive {
    color: #34d399; // text-green-400
  }
  
  &.negative {
    color: #f87171; // text-red-400
  }
}

.waitingMessage {
  text-align: center;
  padding: 2rem 0; // py-8
}

.pageSpecificSection {
  margin-top: 3rem; // mt-12
}

.pageSpecificTitle {
  font-size: 1.5rem; // text-2xl
  font-weight: 600; // font-semibold
  margin-bottom: 1.5rem; // mb-6
}

.pageContainer {
  margin-bottom: 2rem; // mb-8
}

// Dark mode styles
:global(.dark) {
  .title {
    color: #ffffff; // text-white
  }
  
  .noDataText {
    color: #9ca3af; // text-gray-400
  }
  
  .summarySection {
    background-color: rgba(30, 58, 138, 0.3); // bg-blue-900/30
  }
  
  .summaryTitle {
    color: #ffffff; // text-white
  }
  
  .summaryContent {
    color: #d1d5db; // text-gray-300
  }
  
  .impactTitle {
    color: #ffffff; // text-white
  }
  
  .tableContainer {
    background-color: #374151; // bg-gray-700
  }
  
  .tableHeader {
    background-color: #1f2937; // bg-gray-800
  }
  
  .tableHeaderCell {
    color: #ffffff; // text-white
  }
  
  .tableRow {
    border-color: #374151; // border-gray-700
    
    &:hover {
      background-color: rgba(31, 41, 55, 0.3); // hover:bg-gray-800/30
    }
  }
  
  .tableCell {
    color: #e5e7eb; // text-gray-200
  }
  
  .waitingMessage {
    color: #9ca3af; // text-gray-400
  }
  
  .pageSpecificTitle {
    color: #ffffff; // text-white
  }
}
