.container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem 1rem 1.5rem 1rem;
  margin-bottom: 1.5rem;
  
  @media (min-width: 768px) {
    flex-direction: row;
  }
}

.sidebarContainer {
  @media (min-width: 768px) {
    width: 25%;
  }
}

.mainContainer {
  @media (min-width: 768px) {
    width: 75%;
  }
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  padding-left: 0.75rem;
}

.titleDark {
  color: white;
}

.titleLight {
  color: #1f2937;
}

.card {
  background-color: transparent;
  border-color: transparent;
}

.accordionContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.accordionItem {
  backdrop-filter: blur(24px);
  border-radius: 0.5rem;
  padding: 1rem 1.5rem;
}

.accordionItemDark {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
}

.accordionItemLight {
  background-color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(209, 213, 219, 0.3);
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.8);
  }
}

.challengeHeader {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.challengeNumber {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  background-color: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
  font-size: 0.875rem;
  font-weight: 500;
}

.challengeInfo {
  text-align: left;
}

.challengeTitle {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.titleDark {
  color: white;
}

.titleLight {
  color: #1f2937;
}

.challengeDescription {
  font-size: 0.875rem;
  line-height: 1.5;
}

.descriptionDark {
  color: #d1d5db;
}

.descriptionLight {
  color: #374151;
}

.optionsContainer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top-width: 1px;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.borderDark {
  border-color: rgba(255, 255, 255, 0.1);
}

.borderLight {
  border-color: rgba(209, 213, 219, 0.3);
}

.optionItem {
  border-radius: 0.5rem;
  border-width: 1px;
  padding: 1rem;
}

.optionSelected {
  border-color: rgba(59, 130, 246, 0.5);
  background-color: rgba(59, 130, 246, 0.1);
}

.optionDark {
  background-color: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.05);
}

.optionLight {
  background-color: rgba(255, 255, 255, 0.6);
  border-color: rgba(209, 213, 219, 0.3);
}

.optionContent {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.checkboxDark {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.checkboxLight {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.optionLabel {
  font-size: 0.875rem;
  line-height: 1.5;
  cursor: pointer;
}

.labelDark {
  color: #e5e7eb;
}

.labelLight {
  color: #374151;
}

.consequenceBox {
  margin-top: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.375rem;
  border-width: 1px;
}

.consequenceDark {
  background-color: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
}

.consequenceLight {
  background-color: #eff6ff;
  border-color: #bfdbfe;
}

.consequenceText {
  font-size: 0.875rem;
}

.textDark {
  color: #bfdbfe;
}

.textLight {
  color: #1d4ed8;
}