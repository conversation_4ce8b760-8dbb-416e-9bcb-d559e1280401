import React, { Component } from 'react';
import { PageHeader, InputGroup, FormControl, Button, Row, Col, Table, Pagination } from 'react-bootstrap';

import BlockUi from 'react-block-ui';

import { listChallenges, toggleDisabled } from '../../actions/challenge';
import ResultLimit from '../App/ResultLimit';

class Challenges extends Component {
  constructor(props) {
    super(props);
    this.state = {
      query: '',
      currentPage: 1,
      showDisabled: false,
      totalItems: 0,
      limit: 20,
      offset: 0,
      sort: 'scheme_name',
      ascending: true,
      challenges: [],
    };
    this.pageEvent = this.pageEvent.bind(this);
  }

  componentDidMount() {
    this.list();
  }

  handleLimitChange(e) {
    this.setState(
      {
        limit: e.target.value,
        offset: 0,
      },
      () => {
        this.list();
      }
    );
  }

  updateQuery(key, value) {
    this.setState({
      [key]: value,
      currentPage: 1,
      offset: 0,
    });
  }

  list() {
    const { query, limit, offset, showDisabled, sort, ascending } = this.state;
    const { alert } = this.props;

    const q = {
      query,
      limit,
      offset,
      showDisabled,
      sort,
      ascending,
    };
    console.log('querying :', q);
    this.blockUi();
    listChallenges(q).payload.then(
      (results) => {
        console.log('got teams: ', results);
        this.setState({ challenges: results.data });
        if (results.data.length)
          this.setState({
            totalItems: Math.ceil(results.data[0].total / limit),
          });

        this.unBlockUi();
      },
      (error) => {
        console.log(error);
        alert('danger', 'Error', `Error listing challenges: ${error.toString()}`);
        this.unBlockUi();
      }
    );
  }

  viewChallenge(id) {
    const { navigate } = this.props;

    navigate(`/challenges/id/${id}`);
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  sort(column) {
    this.setState(
      (prevState) => ({
        ascending: !prevState.ascending,
        sort: column,
        currentPage: 1,
        offset: 0,
      }),
      () => {
        this.list();
      }
    );
  }

  pageEvent(e) {
    const { limit } = this.state;

    console.log('settig offset: ', (e - 1) * limit);
    this.setState(
      {
        currentPage: e,
        offset: (e - 1) * limit,
      },
      () => {
        this.list();
      }
    );
  }

  toggleChallengeDisable(challenge) {
    this.blockUi();
    toggleDisabled(challenge.id).payload.then(() => {
      this.list();
    });
  }

  toggleShowDisabled() {
    const { showDisabled } = this.state;

    this.setState(
      {
        showDisabled: !showDisabled,
      },
      () => {
        this.list();
      }
    );
  }

  renderButtons(challenge) {
    let disable;
    if (challenge.disabled)
      disable = (
        <Button bsSize="xsmall" bsStyle="info" onClick={() => this.toggleChallengeDisable(challenge)}>
          Enable
        </Button>
      );
    else
      disable = (
        <Button bsSize="xsmall" onClick={() => this.toggleChallengeDisable(challenge)}>
          Disable
        </Button>
      );

    return (
      <td className="text-right">
        <Button bsSize="xsmall" onClick={() => this.viewChallenge(challenge.id)} style={{ marginRight: '10px' }}>
          View
        </Button>
        {disable}
      </td>
    );
  }

  renderChallengesList() {
    const { challenges } = this.state;

    return (
      <tbody>
        {challenges.map((challenge, i) => (
          <tr key={i}>
            <td>{challenge.name}</td>
            <td className="text-right">{challenge.created}</td>
            {this.renderButtons(challenge)}
          </tr>
        ))}
      </tbody>
    );
  }

  render() {
    const { showDisabled, blocking, totalItems, currentPage } = this.state;
    const { navigate } = this.props;

    return (
      <div className="container root-container">
        <PageHeader>Challenges</PageHeader>
        <Row>
          <Col lg={2} md={2} xs={6}>
            <ResultLimit onChange={(e) => this.handleLimitChange(e)} />
          </Col>
          <Col lg={4} md={4} xs={6}>
            <InputGroup>
              <FormControl
                type="text"
                placeholder="Search"
                name="query"
                onChange={(e) => this.updateQuery('query', e.target.value)}
              />
              <InputGroup.Button>
                <Button type="submit" className="pull-right" name="list" onClick={() => this.list()}>
                  Go
                </Button>
              </InputGroup.Button>
            </InputGroup>
          </Col>
          <Col lg={6} md={6} xs={12}>
            <Button className="pull-right" bsStyle="primary" onClick={() => navigate('/challenges/add')}>
              Add Challenge
            </Button>

            <Button
              className="pull-right"
              bsStyle={showDisabled ? 'success' : 'default'}
              onClick={() => this.toggleShowDisabled()}
              style={{ marginRight: '10px' }}
            >
              Show Disabled
            </Button>
          </Col>
        </Row>
        <br />
        <Row>
          <Col md={12}>
            <BlockUi tag="div" blocking={blocking}>
              <Table striped bordered hover>
                <thead className="thead-light">
                  <tr>
                    <th role="button" onClick={() => this.sort('scheme_name')}>
                      Name
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th role="button" className="text-right" onClick={() => this.sort('scheme_created_at')}>
                      Created At
                      <i className="glyphicon glyphicon-sort" style={{ marginLeft: '5px' }} />
                    </th>
                    <th className="text-right">Actions</th>
                  </tr>
                </thead>
                {this.renderChallengesList()}
              </Table>
            </BlockUi>
          </Col>
        </Row>
        <Row>
          <Col md={12}>
            <Pagination
              className="pull-right"
              prev
              next
              first
              last
              boundaryLinks
              items={totalItems}
              maxButtons={5}
              activePage={currentPage}
              onSelect={this.pageEvent}
            />
          </Col>
        </Row>
      </div>
    );
  }
}

export default Challenges;
