import React, { useState, useEffect, useRef } from "react";
import { toast } from "sonner";
import to from "await-to-js";
import { useTheme } from "@/components/ThemeProvider/ThemeProvider";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import Loader from "../common/Loader";
import { getChallenges, saveChallenges, getTeam } from "../../actions/user";
import Metrics from "../Metrics/Metrics";
import { motion, AnimatePresence } from "framer-motion";
import styles from "./Challenges.module.css";
import ChallengeConnector from "./ChallengeConnector";
import GroupPlaceholder from "../common/GroupPlaceholder";

const Challenges = ({ user, navigate, location }) => {
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedChallengeId, setSelectedChallengeId] = useState(null); // Track which challenge is selected for modal
  const [challengeData, setChallengeData] = useState({
    challengeNumber: 0,
    challenges: [],
    globalTeamMetrics: [],
    currentChallengeId: null,
    altMap: new Map(), // Map of regular challenge IDs to their alt versions
  });
  const [activeChallenge, setActiveChallenge] = useState(0);
  const [animating, setAnimating] = useState(false);
  const [circlePositions, setCirclePositions] = useState(new Map()); // Store consistent circle positions
  const challengeContainerRef = useRef(null); // Ref for the challenge container
  const [backgroundBubbles, setBackgroundBubbles] = useState([]); // Background floating bubbles

  const client = user?.client || {};
  const { challengesTabName, challengesTabVisibility, darkHighlightColor, lightHighlightColor } = client;

  useEffect(() => {
    // Fetch challenges immediately when component mounts
    fetchChallenges();
    // Initialize background bubbles
    generateBackgroundBubbles();
  }, []);

  // Generate background floating bubbles
  const generateBackgroundBubbles = () => {
    const bubbles = [];
    const bubbleCount = 12; // Number of background bubbles

    for (let i = 0; i < bubbleCount; i++) {
      bubbles.push({
        id: i,
        size: Math.random() * 40 + 15, // Random size between 15-55px (smaller, more subtle)
        left: Math.random() * 100, // Random horizontal position (%)
        animationDuration: Math.random() * 15 + 20, // Random duration between 20-35s (slower)
        animationDelay: Math.random() * 30, // Random delay up to 30s (more spread out)
      });
    }

    setBackgroundBubbles(bubbles);
  };

  // Helper function to generate consistent circle positions
  const generateCirclePositions = (challenges) => {
    const positions = new Map();

    challenges.forEach(challenge => {
      if (!challenge.isAltChallenge) {
        const getSizeInfo = (bubbleSize) => {
          switch (bubbleSize) {
            case 'medium':
              return { width: 115 };
            case 'large':
              return { width: 134 };
            case 'small':
            default:
              return { width: 96 };
          }
        };

        const sizeInfo = getSizeInfo(challenge.bubbleSize);
        const randomX = (Math.random() - 0.5) * sizeInfo.width * 0.75;
        const randomY = (Math.random() - 0.5) * sizeInfo.width * 0.75;

        positions.set(challenge.id, {
          x: randomX,
          y: randomY,
          width: sizeInfo.width
        });
      }
    });

    return positions;
  };

  // Helper functions for alt challenge logic
  const findAltChallenge = (challengeId, challenges) => {
    return challenges.find(challenge =>
      challenge.isAltChallenge && challenge.altOfChallenge === challengeId
    );
  };

  const getNextChallengeIndex = (currentIndex, selectedOption, challenges) => {
    const currentChallenge = challenges[currentIndex];
    const nextSequentialIndex = currentIndex + 1;

    // If we're at the last challenge, return null
    if (nextSequentialIndex >= challenges.length) {
      return null;
    }

    // Check if the selected option should route to an alt challenge
    const shouldUseAlt = currentChallenge[`option${selectedOption}IsAlt`];

    if (shouldUseAlt) {
      // Find the alt version of the next challenge
      const nextChallenge = challenges[nextSequentialIndex];
      const altChallenge = findAltChallenge(nextChallenge.id, challenges);

      if (altChallenge) {
        // Find the index of the alt challenge
        return challenges.findIndex(challenge => challenge.id === altChallenge.id);
      }
    }

    // Default to next sequential challenge
    return nextSequentialIndex;
  };

  const buildChallengeSequence = (challenges) => {
    // Create a map of regular challenges to their alt versions
    const altMap = new Map();
    challenges.forEach(challenge => {
      if (challenge.isAltChallenge && challenge.altOfChallenge) {
        altMap.set(challenge.altOfChallenge, challenge);
      }
    });

    return { challenges, altMap };
  };

  const getChallengeDisplayInfo = (challenge, challengeIndex, challenges) => {
    if (challenge.isAltChallenge && challenge.altOfChallenge) {
      // Find the original challenge to get its position
      const originalChallenge = challenges.find(c => c.id === challenge.altOfChallenge);
      if (originalChallenge) {
        const originalIndex = challenges.findIndex(c => c.id === originalChallenge.id);
        // Count only non-alt challenges before this one for numbering
        const nonAltChallengesBefore = challenges.slice(0, originalIndex).filter(c => !c.isAltChallenge).length;
        return {
          number: nonAltChallengesBefore + 1,
          title: `Challenge #${nonAltChallengesBefore + 1} (Alternative)`,
          isAlt: true
        };
      }
    }

    // For regular challenges, count non-alt challenges before this one
    const nonAltChallengesBefore = challenges.slice(0, challengeIndex).filter(c => !c.isAltChallenge).length;
    return {
      number: nonAltChallengesBefore + 1,
      title: `Challenge #${nonAltChallengesBefore + 1}`,
      isAlt: false
    };
  };

  const calculateActiveChallenge = (challenges) => {
    // Simulate the user's journey through challenges to find where they should be
    let currentIndex = 0;

    while (currentIndex < challenges.length) {
      const challenge = challenges[currentIndex];

      // If this challenge hasn't been submitted, this is where the user should be
      if (!challenge.submitted) {
        return currentIndex;
      }

      // Find which option was selected
      let selectedOption = null;
      if (challenge.selectedA) selectedOption = 'A';
      else if (challenge.selectedB) selectedOption = 'B';
      else if (challenge.selectedC) selectedOption = 'C';

      if (selectedOption) {
        // Use the same logic as getNextChallengeIndex to determine where they went next
        const nextIndex = getNextChallengeIndex(currentIndex, selectedOption, challenges);
        if (nextIndex === null) {
          // They completed all challenges
          return challenges.length;
        }
        currentIndex = nextIndex;
      } else {
        // Fallback to next sequential challenge if no selection found
        currentIndex++;
      }
    }

    // All challenges completed
    return challenges.length;
  };

  const fetchChallenges = async () => {
    setIsLoading(true);
    const [err, res] = await to(getChallenges().payload);

    const team = await getTeam().payload;

    if (err) {
      toast.error("Failed to load challenges");
      setIsLoading(false);
      return;
    }

    const { data } = res;
    // Sort challenges and initialize state
    const sortedChallenges = data.challenges
      .sort((a, b) => a.id - b.id)
      .map((challenge) => ({
        ...challenge,
        selectedA: challenge.selectedA || false,
        selectedB: challenge.selectedB || false,
        selectedC: challenge.selectedC || false,
        submitted:
          challenge.selectedA ||
          challenge.selectedB ||
          challenge.selectedC ||
          false,
        showOptions: false,
        // Ensure alt challenge properties are available
        altOfChallenge: challenge.altOfChallenge || null,
        isAltChallenge: challenge.isAltChallenge || false,
        optionAIsAlt: challenge.optionAIsAlt || false,
        optionBIsAlt: challenge.optionBIsAlt || false,
        optionCIsAlt: challenge.optionCIsAlt || false,
      }));

    // Build challenge sequence with alt mappings
    const { challenges, altMap } = buildChallengeSequence(sortedChallenges);

    // Initialize metrics with 0 values
    const globalTeamMetrics = (team.data.globalTeamMetrics || []).map(metric => ({
      ...metric,
      value: 0
    }));

    // Calculate metrics based on selected options
    sortedChallenges.forEach(challenge => {
      ['A', 'B', 'C'].forEach(option => {
        if (challenge[`selected${option}`]) {
          globalTeamMetrics.forEach(metric => {
            const [, metricIndex] = metric.alias.split(" ");
            const alias = `optionMetric${metricIndex}${option.toUpperCase()}`;
            metric.value = (metric.value || 0) + (challenge[alias] || 0);
          });
        }
      });
    });

    // Calculate the correct active challenge based on user's previous selections
    const correctActiveChallenge = calculateActiveChallenge(challenges);

    // Generate consistent circle positions
    const positions = generateCirclePositions(challenges);

    setChallengeData({
      ...data,
      challenges: challenges,
      currentChallengeId: challenges[0].id,
      globalTeamMetrics,
      altMap
    });

    // Set the correct active challenge and circle positions
    setActiveChallenge(correctActiveChallenge);
    setCirclePositions(positions);
    setIsLoading(false);
  };

  const handleOptionChange = async (option, challengeIndex, checked) => {
    const updatedChallenges = [...challengeData.challenges];
    const challenge = updatedChallenges[challengeIndex];
    const updatedMetrics = [...challengeData.globalTeamMetrics];

    if (!checked || challenge.submitted) return;

    // Reset all options for this challenge
    challenge.selectedA = false;
    challenge.selectedB = false;
    challenge.selectedC = false;
    challenge[`selected${option}`] = checked;
    challenge.submitted = true;

    // Recalculate all metrics from scratch
    updatedMetrics.forEach((metric) => {
      const [, metricIndex] = metric.alias.split(" ");
      metric.value = 0;
      updatedChallenges.forEach((ch) => {
        ['A', 'B', 'C'].forEach((opt) => {
          if (ch[`selected${opt}`]) {
            const alias = `optionMetric${metricIndex}${opt}`;
            metric.value += (ch[alias] || 0);
          }
        });
      });
    });

    // Optimistically update the state
    setChallengeData((prev) => ({
      ...prev,
      challenges: updatedChallenges,
      globalTeamMetrics: updatedMetrics,
    }));

    // Save changes
    try {
      await saveChallenges({
        ...challengeData,
        challenges: updatedChallenges,
        globalTeamMetrics: updatedMetrics,
      }).payload;
      toast.success("Response saved successfully", {
        duration: 3000,
      });

      // Move to next challenge with animation (considering alt challenges) only if this was the active challenge
      const isActiveChallenge = challengeIndex === activeChallenge;
      if (isActiveChallenge) {
        const nextChallengeIndex = getNextChallengeIndex(activeChallenge, option, challengeData.challenges);

        if (nextChallengeIndex !== null) {
          setAnimating(true);
          setTimeout(() => {
            setActiveChallenge(nextChallengeIndex);
            setAnimating(false);
          }, 500);
        }
      }

      // Don't close the modal automatically - let user read the consequence
    } catch (err) {
      toast.error("Failed to save challenge response");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) return <Loader />;

  // Get the selected challenge for the modal (or current active challenge as fallback)
  const selectedChallenge = selectedChallengeId
    ? challengeData.challenges.find(c => c.id === selectedChallengeId)
    : challengeData.challenges[activeChallenge];

  // Ensure we have a valid selected challenge
  const validSelectedChallenge = selectedChallenge || challengeData.challenges[activeChallenge];

  // Get the current active challenge (for progression logic)
  const currentChallenge = challengeData.challenges[activeChallenge];

  // Helper function to find group/level info for a specific challenge
  const getGroupInfo = (challenge) => {
    if (!challenge) return { groupIndex: 0, isLevel: false, groupName: 'Group 1' };

    const mainChallenges = challengeData.challenges.filter(c => !c.isAltChallenge);
    const allUngrouped = mainChallenges.every(c => !c.groupId || c.groupId === 0);

    if (allUngrouped) {
      // Find which level this challenge belongs to (2 challenges per level)
      const challengeIndex = mainChallenges.findIndex(c => c.id === challenge.id);
      const levelNumber = Math.floor(challengeIndex / 2) + 1;
      return {
        groupIndex: levelNumber - 1,
        isLevel: true,
        groupName: `Level ${levelNumber}`,
        groupId: levelNumber
      };
    } else {
      // Original group-based logic
      const uniqueGroupIds = [...new Set(mainChallenges.map(c => c.groupId || 0))].sort((a, b) => a - b);
      const groupIndex = uniqueGroupIds.indexOf(challenge.groupId || 0);
      return {
        groupIndex,
        isLevel: false,
        groupName: challenge.groupName || 'Ungrouped',
        groupId: challenge.groupId || 0
      };
    }
  };

  // Get group info for the selected challenge (for modal display)
  const selectedGroupInfo = getGroupInfo(validSelectedChallenge);

  // Get group info for the current active challenge (for progression logic)
  const currentGroupInfo = getGroupInfo(currentChallenge);

  // Helper function to get random bubble size when none is specified
  const getRandomBubbleSize = () => {
    const sizes = ['small', 'medium', 'large'];
    return sizes[Math.floor(Math.random() * sizes.length)];
  };

  // Get completed challenges - only show challenges that were actually completed by the user
  // Don't show challenges that were skipped due to alt routing
  const getCompletedChallenges = () => {
    const completed = [];
    let currentIndex = 0;

    while (currentIndex < activeChallenge && currentIndex < challengeData.challenges.length) {
      const challenge = challengeData.challenges[currentIndex];

      // Only include challenges that were actually submitted
      if (challenge.submitted) {
        completed.push(challenge);

        // Find which option was selected to determine the next challenge
        let selectedOption = null;
        if (challenge.selectedA) selectedOption = 'A';
        else if (challenge.selectedB) selectedOption = 'B';
        else if (challenge.selectedC) selectedOption = 'C';

        if (selectedOption) {
          // Use the same logic to find where they went next
          const nextIndex = getNextChallengeIndex(currentIndex, selectedOption, challengeData.challenges);
          if (nextIndex === null) break;
          currentIndex = nextIndex;
        } else {
          currentIndex++;
        }
      } else {
        currentIndex++;
      }
    }

    return completed;
  };

  const completedChallenges = getCompletedChallenges();

  // Don't render anything if challenges are not visible
  if (client?.hasOwnProperty("challengesTabVisibility") && !challengesTabVisibility) {
    return null;
  }

  return (
    <div className={styles.container}>
      {/* Metrics Section - Always visible on the left */}
      <div className={styles.metricsSection}>
        <h2 className={`${styles.metricsTitle} ${theme === 'dark' ? styles.darkText : styles.lightText}`}>
          Metrics
        </h2>
        <Metrics
          location={location}
          client={client}
          metrics={challengeData.globalTeamMetrics}
          setGlobalMetrics={(metrics) =>
            setChallengeData((prev) => ({ ...prev, globalTeamMetrics: metrics }))
          }
        />
      </div>

      {/* Group Dividers Section */}
      <div className={styles.groupDividersContainer}>
        <div className="mt-20">
          {[...new Set(challengeData.challenges
            .filter(challenge => !challenge.isAltChallenge)
            .map(challenge => ({
              groupId: challenge.groupId || 0,
              groupName: challenge.groupName || 'Ungrouped'
            }))
            .sort((a, b) => a.groupId - b.groupId)
            .map(group => group.groupName)
          )].map((groupName, index, array) => (
            <React.Fragment key={index}>
              <div className="flex items-center gap-4">
                {/* Short divider line on the left */}
                <div className={`w-8 h-px ${
                  theme === 'dark' ? 'bg-white/20' : 'bg-gray-300/50'
                }`}></div>

                {/* Group Name */}
                <h3 className={`text-lg font-semibold whitespace-nowrap ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  {groupName}
                </h3>
              </div>

              {/* Vertical divider lines between groups (except after the last group) */}
              {
                <div className="flex flex-col justify-between py-0">
                  {Array.from({ length: 8 }).map((_, dividerIndex) => (
                    <div
                      key={dividerIndex}
                      className={`w-6 my-3 h-px ${
                        theme === 'dark' ? 'bg-white/15' : 'bg-gray-300/40'
                      }`}
                    ></div>
                  ))}
                </div>
              }
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Main Content with Modal */}
      <div className={styles.mainContent}>
        {/* Background floating bubbles */}
        <div className={styles.backgroundBubbles}>
          {backgroundBubbles.map((bubble) => (
            <div
              key={bubble.id}
              className={`${styles.backgroundBubble} ${
                theme === 'dark' ? styles.darkBackgroundBubble : styles.lightBackgroundBubble
              }`}
              style={{
                width: `${bubble.size}px`,
                height: `${bubble.size}px`,
                left: `${bubble.left}%`,
                animationDuration: `${bubble.animationDuration}s`,
                animationDelay: `${bubble.animationDelay}s`,
              }}
            />
          ))}
        </div>

        <div className="flex flex-col items-center justify-center min-h-[400px] gap-4 relative z-10">
          <h2 className={`${styles.challengesTitle} ${theme === 'dark' ? styles.darkText : styles.lightText}`}>
            {challengesTabName || "Challenges"}
          </h2>

          {/* Challenge Groups */}
          <div className="w-full max-w-4xl relative" ref={challengeContainerRef}>
            {/* SVG Overlay for curved lines */}
            <ChallengeConnector
              challenges={challengeData.challenges}
              circlePositions={circlePositions}
              containerRef={challengeContainerRef}
            />

            {challengeData.challenges.length > 0 ? (
              (() => {
                // Group challenges by groupId, excluding alt challenges
                const mainChallenges = challengeData.challenges.filter(challenge => !challenge.isAltChallenge);

                // Check if all challenges are ungrouped (all have groupId 0 or null/undefined)
                const allUngrouped = mainChallenges.every(challenge => !challenge.groupId || challenge.groupId === 0);

                let sortedGroups;

                if (allUngrouped && mainChallenges.length > 0) {
                  // Create level-based grouping: 2 challenges per level
                  const challengesPerLevel = 2;
                  const levels = [];

                  for (let i = 0; i < mainChallenges.length; i += challengesPerLevel) {
                    const levelNumber = Math.floor(i / challengesPerLevel) + 1;
                    const levelChallenges = mainChallenges.slice(i, i + challengesPerLevel);

                    levels.push({
                      groupId: levelNumber, // Use level number as groupId
                      groupName: `Level ${levelNumber}`,
                      challenges: levelChallenges,
                      isLevel: true // Flag to indicate this is a level-based group
                    });
                  }

                  sortedGroups = levels;
                } else {
                  // Use original groupId-based grouping
                  const groupedChallenges = mainChallenges.reduce((groups, challenge) => {
                    const groupId = challenge.groupId || 0;
                    const groupName = challenge.groupName || 'Ungrouped';

                    if (!groups[groupId]) {
                      groups[groupId] = {
                        groupId,
                        groupName,
                        challenges: [],
                        isLevel: false
                      };
                    }
                    groups[groupId].challenges.push(challenge);
                    return groups;
                  }, {});

                  // Sort groups by groupId (lowest first)
                  sortedGroups = Object.values(groupedChallenges).sort((a, b) => a.groupId - b.groupId);
                }

                return sortedGroups.map((group, groupIndex) => (
                  <div key={group.groupId} className={styles.challengeGroupContainer} data-group-id={group.groupId}>
                    {/* Challenge Circles for this group */}
                    <div className="flex gap-4 justify-evenly items-center">

                      {/* Challenge circles container with its own padding */}
                      <div className={`flex flex-wrap gap-4 justify-evenly items-center flex-1 h-[228px] my-0 pl-4 ${styles.challengeCirclesContainer}`}>
                        {group.challenges.map((challenge) => {
                        const challengeIndex = challengeData.challenges.findIndex(c => c.id === challenge.id);
                        const displayInfo = getChallengeDisplayInfo(challenge, challengeIndex, challengeData.challenges);

                        // Use random bubble size if none is specified
                        const effectiveBubbleSize = challenge.bubbleSize || getRandomBubbleSize();

                        // Calculate size based on bubbleSize property
                        const getSizeClasses = (bubbleSize) => {
                          switch (bubbleSize) {
                            case 'medium':
                              return { classes: 'w-[115px] h-[115px]', width: 115 }; // 20% larger than 96px
                            case 'large':
                              return { classes: 'w-[134px] h-[134px]', width: 134 }; // 40% larger than 96px
                            case 'small':
                            default:
                              return { classes: 'w-24 h-24', width: 96 }; // 96px (default/small size)
                          }
                        };

                        const sizeInfo = getSizeClasses(effectiveBubbleSize);
                        const sizeClasses = sizeInfo.classes;

                        // Get stored position for this challenge
                        const storedPosition = circlePositions.get(challenge.id) || { x: 0, y: 0 };
                        const randomX = storedPosition.x;
                        const randomY = storedPosition.y;

                        // Check if this is the current challenge (the one the user should answer next)
                        const isCurrentChallenge = challengeData.challenges.findIndex(c => c.id === challenge.id) === activeChallenge;

                        return (
                          <Dialog key={challenge.id} open={isModalOpen} onOpenChange={setIsModalOpen}>
                            <DialogTrigger asChild>
                              <div
                                className={`flex flex-col items-center gap-2 relative z-10 ${styles.challengeCircle}`}
                                style={{
                                  '--translate-x': `${randomX}px`,
                                  '--translate-y': `${randomY}px`,
                                  '--float-delay': `${(challenge.id * 0.5) % 6}s` // Stagger animation delays
                                }}
                                onClick={() => setSelectedChallengeId(challenge.id)}
                              >
                                <div
                                  data-challenge-id={challenge.id}
                                  data-is-current={isCurrentChallenge}
                                  className={`
                                    ${sizeClasses} cursor-pointer transition-colors duration-200
                                    flex items-center justify-center border-2 relative overflow-hidden
                                    ${theme === 'dark' ? 'border-white/20 hover:border-white/40' : 'border-gray-300/30 hover:border-gray-400/50'}
                                    ${isCurrentChallenge ? `${styles.currentChallengePulse} ${theme === 'dark' ? styles.darkCurrentChallengeBorder : styles.currentChallengeBorder}` : ''}
                                  `}
                                  style={{
                                    borderRadius: '50%',
                                    backgroundImage: challenge.imageUrl ? `url(${challenge.imageUrl})` : 'none',
                                    backgroundSize: 'cover',
                                    backgroundPosition: 'center',
                                    backgroundColor: challenge.imageUrl ? 'transparent' : (theme === 'dark' ? '#4a4945' : 'rgba(0, 0, 0, 0.1)')
                                  }}
                                >
                                  {!challenge.imageUrl && (
                                    <GroupPlaceholder
                                      groupId={group.groupId}
                                      groupName={group.groupName}
                                      groupIndex={groupIndex}
                                      size={effectiveBubbleSize === 'large' ? 'large' : effectiveBubbleSize === 'small' ? 'small' : 'medium'}
                                      theme={theme}
                                      primaryColor={theme === 'dark' ? darkHighlightColor : lightHighlightColor}
                                      isLevel={group.isLevel}
                                      challengeNumber={challengeIndex + 1}
                                    />
                                  )}
                                </div>

                                {/* Title below the circle */}
                                <span
                                  className={`text-xs font-semibold text-center max-w-[120px] leading-tight ${
                                    theme === 'dark' ? 'text-white' : 'text-gray-800'
                                  }`}
                                  style={{
                                    textShadow: theme === 'dark'
                                      ? '1px 1px 2px rgba(0, 0, 0, 0.8)'
                                      : '1px 1px 2px rgba(255, 255, 255, 0.8)',
                                    zIndex: 10
                                  }}
                                >
                                  {displayInfo.title}
                                </span>
                              </div>
                            </DialogTrigger>
                          </Dialog>
                        );
                      })}
                      </div>
                    </div>
                  </div>
                ));
              })()
            ) : (
              <div className={`
                w-24 h-24 rounded-full flex items-center justify-center text-sm font-medium mx-auto
                ${theme === 'dark' ? 'bg-gray-600 text-gray-300' : 'bg-gray-300 text-gray-600'}
              `}>
                No Challenges
              </div>
            )}
          </div>

          {/* Single Modal for all challenges */}
          <Dialog open={isModalOpen} onOpenChange={(open) => {
            setIsModalOpen(open);
            if (!open) {
              setSelectedChallengeId(null); // Reset selected challenge when modal closes
            }
          }}>
            {/* Challenge Circle Copy in Corner - Outside DialogContent to avoid clipping */}
            {!!validSelectedChallenge && isModalOpen && (
              <div
                className="fixed pointer-events-none"
                style={{
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  zIndex: 9999
                }}
              >
                <div
                  className="absolute pointer-events-none"
                  style={{
                    top: `calc(-45vh + 0px)`, // Position at top of modal
                    left: `calc(-50vw + 0px)`, // Position at left of modal
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  <div
                    className={`
                      ${(() => {
                        const getSizeClasses = (bubbleSize) => {
                          switch (bubbleSize) {
                            case 'medium':
                              return 'w-[115px] h-[115px]';
                            case 'large':
                              return 'w-[134px] h-[134px]';
                            case 'small':
                            default:
                              return 'w-24 h-24';
                          }
                        };
                        return getSizeClasses(validSelectedChallenge.bubbleSize);
                      })()}
                      flex items-center justify-center text-white font-semibold text-sm text-center
                      border-2 relative overflow-hidden
                      ${theme === 'dark' ? 'border-white/20' : 'border-gray-300/30'}
                    `}
                    style={{
                      borderRadius: '50%',
                      backgroundImage: validSelectedChallenge.imageUrl ? `url(${validSelectedChallenge.imageUrl})` : 'none',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      backgroundColor: validSelectedChallenge.imageUrl ? 'transparent' : (theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)')
                    }}
                  >
                    {!validSelectedChallenge.imageUrl && (
                      <GroupPlaceholder
                        groupId={selectedGroupInfo.groupId}
                        groupName={selectedGroupInfo.groupName}
                        groupIndex={selectedGroupInfo.groupIndex}
                        size="large"
                        theme={theme}
                        primaryColor={theme === 'dark' ? darkHighlightColor : lightHighlightColor}
                        isLevel={selectedGroupInfo.isLevel}
                        challengeNumber={challengeData.challenges.findIndex(c => c.id === validSelectedChallenge.id) + 1}
                      />
                    )}
                    {validSelectedChallenge.imageUrl && (
                      <div
                        className="absolute inset-0 bg-black/30 flex items-center justify-center"
                        style={{ borderRadius: '50%' }}
                      >
                        <span className="text-xs px-2 text-white font-medium">
                          {getChallengeDisplayInfo(validSelectedChallenge, challengeData.challenges.findIndex(c => c.id === validSelectedChallenge.id), challengeData.challenges).title}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            <DialogContent className={`max-w-5xl max-h-[90vh] p-0 backdrop-blur-xl ${
              theme === 'dark'
                ? 'bg-black/20 border-white/40'
                : 'bg-white/30 border-gray-300/30'
            }`}>
              {/* Challenge Circle Copy in Corner */}
              {!!validSelectedChallenge && (
                <div
                  className="absolute pointer-events-none"
                  style={{
                    top: '0px',
                    left: '0px',
                    transform: 'translate(-50%, -50%)',
                    zIndex: 1000
                  }}
                >
                  <div
                    className="w-[115px] h-[115px] flex items-center justify-center text-white font-semibold text-sm text-center border-2 overflow-hidden"
                    style={{
                      borderRadius: '50%',
                      backgroundImage: validSelectedChallenge.imageUrl ? `url(${validSelectedChallenge.imageUrl})` : 'none',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      backgroundColor: validSelectedChallenge.imageUrl ? 'transparent' : 'rgba(0, 0, 0, 0.8)',
                      borderColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.8)'
                    }}
                  >
                    {!validSelectedChallenge.imageUrl && (
                      <GroupPlaceholder
                        groupId={selectedGroupInfo.groupId}
                        groupName={selectedGroupInfo.groupName}
                        groupIndex={selectedGroupInfo.groupIndex}
                        size="large"
                        theme={theme}
                        primaryColor={theme === 'dark' ? darkHighlightColor : lightHighlightColor}
                        isLevel={selectedGroupInfo.isLevel}
                        challengeNumber={challengeData.challenges.findIndex(c => c.id === validSelectedChallenge.id) + 1}
                      />
                    )}
                    {validSelectedChallenge.imageUrl && (
                      <div
                        className="absolute inset-0 flex items-center justify-center"
                        style={{ borderRadius: '50%' }}
                      >
                        <span className="text-xs px-2 text-white font-medium">
                          {getChallengeDisplayInfo(validSelectedChallenge, challengeData.challenges.findIndex(c => c.id === validSelectedChallenge.id), challengeData.challenges).title}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

          {isLoading ? (
            <div className="flex items-center justify-center h-96">
              <Loader />
            </div>
          ) : (
            <div className="overflow-y-auto max-h-[calc(90vh-120px)] pt-20 p-6">
              {/* Selected challenge for modal display */}
              {!!validSelectedChallenge ? (
                <Card className="bg-transparent border-transparent">
                  <motion.div
                    key={validSelectedChallenge.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                    className={`${styles.challengeCard} ${
                      theme === 'dark'
                        ? `${styles.darkCard} ${styles.darkCardHover}`
                        : `${styles.lightCard} ${styles.lightCardHover}`
                    }`}
                  >
                    <div className={styles.challengeHeader}>
                      <div>
                        <h3 className={`${styles.challengeTitle} ${theme === 'dark' ? styles.darkText : styles.lightText}`}>
                          {getChallengeDisplayInfo(validSelectedChallenge, challengeData.challenges.findIndex(c => c.id === validSelectedChallenge.id), challengeData.challenges).title}
                        </h3>
                        <p className={`${styles.challengeDescription} ${theme === 'dark' ? styles.darkGrayText : styles.lightGrayText}`}>
                          {validSelectedChallenge.description}
                        </p>
                      </div>
                    </div>

                    <div className={styles.optionsContainer}>
                      {["A", "B", "C"].map((option) => {
                        const selectedChallengeIndex = challengeData.challenges.findIndex(c => c.id === validSelectedChallenge.id);
                        return (
                        <div
                          key={option}
                          className={`${styles.optionCard} ${
                            validSelectedChallenge[`selected${option}`]
                              ? styles.darkSelectedOption
                              : theme === 'dark'
                                ? styles.darkOptionCard
                                : styles.lightOptionCard
                          }`}
                        >
                          <div className={styles.optionContent}>
                            <Checkbox
                              id={`challenge-${selectedChallengeIndex}-option${option}`}
                              checked={validSelectedChallenge[`selected${option}`]}
                              onCheckedChange={(checked) =>
                                handleOptionChange(option, selectedChallengeIndex, checked)
                              }
                              disabled={validSelectedChallenge.submitted || animating}
                              className={`${styles.checkbox} ${theme === 'dark' ? styles.darkCheckbox : styles.lightCheckbox}`}
                            />
                            <div className="flex-1">
                              <label
                                htmlFor={`challenge-${selectedChallengeIndex}-option${option}`}
                                className={`${styles.optionLabel} ${theme === 'dark' ? styles.darkGrayText : styles.lightGrayText}`}
                              >
                                {validSelectedChallenge[`option${option}`]}
                              </label>
                              {validSelectedChallenge[`selected${option}`] && (
                                <div className={`${styles.consequenceBox} ${
                                  theme === 'dark'
                                    ? styles.darkConsequenceBox
                                    : styles.lightConsequenceBox
                                }`}>
                                  <p className={`${styles.consequenceText} ${
                                    theme === 'dark' ? styles.darkConsequenceText : styles.lightConsequenceText
                                  }`}>
                                    Consequence: {validSelectedChallenge[`consequence${option}`]}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        );
                      })}
                    </div>
                  </motion.div>
                </Card>
              ) : (
                /* Completion message */
                <Card className="bg-transparent border-transparent">
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className={`${styles.completionMessage} ${
                      theme === 'dark' ? `${styles.darkCompletionCard} ${styles.darkText}` : `${styles.lightCompletionCard} ${styles.lightText}`
                    }`}
                  >
                    <h3 className={styles.completionTitle}>All Challenges Completed!</h3>
                    <p>You have completed all available challenges.</p>
                  </motion.div>
                </Card>
              )}
            </div>
          )}
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default Challenges;
