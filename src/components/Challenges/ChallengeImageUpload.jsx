import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>abel, Button } from 'react-bootstrap';
import cn from 'classnames';
import to from 'await-to-js';

import { uploadChallengeImage } from '../../actions/challenge';
import styles from '../ClientDetails/client-details.module.scss';

const ChallengeImageUpload = ({ 
  challengeIndex, 
  currentImageUrl, 
  onImageUploaded, 
  onImageRemoved,
  alert 
}) => {
  const [uploading, setUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState(currentImageUrl);
  const uploadId = `challenge-image-upload-${challengeIndex}`;

  // Update imageUrl when currentImageUrl prop changes (when challenge data loads)
  useEffect(() => {
    setImageUrl(currentImageUrl);
  }, [currentImageUrl]);

  const validateFile = (file) => {
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!validTypes.includes(file.type)) {
      alert('danger', 'Error', 'Please upload a valid image file (JPEG, PNG, GIF, or WebP)');
      return false;
    }

    if (file.size > maxSize) {
      alert('danger', 'Error', 'Image file size must be less than 5MB');
      return false;
    }

    return true;
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!validateFile(file)) {
      // Clear the input
      event.target.value = '';
      return;
    }

    setUploading(true);
    
    try {
      const [err, response] = await to(uploadChallengeImage(file));

      if (err) {
        console.error('Upload error:', err);
        alert('danger', 'Error', 'Failed to upload image. Please try again.');
        return;
      }

      const { url, originalName } = response.data;
      setImageUrl(url);
      onImageUploaded(challengeIndex, url);
      alert('success', 'Success', `Image "${originalName}" uploaded successfully`);
    } catch (error) {
      console.error('Upload error:', error);
      alert('danger', 'Error', 'Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
      // Clear the input so the same file can be selected again if needed
      event.target.value = '';
    }
  };

  const handleRemoveImage = () => {
    if (window.confirm('Are you sure you want to remove this image?')) {
      setImageUrl('');
      onImageRemoved(challengeIndex);
      // Clear the file input
      const input = document.getElementById(uploadId);
      if (input) {
        input.value = '';
      }
    }
  };

  return (
    <FormGroup>
      <ControlLabel>Challenge Image (Optional)</ControlLabel>
      
      {imageUrl && (
        <div className={styles.preview} style={{ marginBottom: '10px' }}>
          <img 
            src={imageUrl} 
            alt="Challenge" 
            className={cn('img-responsive', styles.img)}
            style={{ maxWidth: '200px', maxHeight: '150px', objectFit: 'cover' }}
          />
          <Button 
            bsStyle="danger" 
            className={styles.button}
            onClick={handleRemoveImage}
            disabled={uploading}
            style={{ marginTop: '10px' }}
          >
            Remove Image
          </Button>
        </div>
      )}

      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
        <input
          type="file"
          accept="image/jpeg,image/png,image/gif,image/webp"
          onChange={handleFileUpload}
          disabled={uploading}
          style={{ display: 'none' }}
          id={uploadId}
        />
        <label htmlFor={uploadId}>
          <Button 
            bsStyle="primary" 
            disabled={uploading}
            onClick={() => document.getElementById(uploadId).click()}
          >
            {uploading ? 'Uploading...' : (imageUrl ? 'Replace Image' : 'Upload Image')}
          </Button>
        </label>
        
        {uploading && (
          <small className="text-muted">
            <i className="glyphicon glyphicon-refresh glyphicon-spin" style={{ marginRight: '5px' }} />
            Uploading image...
          </small>
        )}
      </div>

      <small className="text-muted" style={{ display: 'block', marginTop: '5px' }}>
        Supported formats: JPEG, PNG, GIF, WebP (max 5MB)
      </small>
    </FormGroup>
  );
};

export default ChallengeImageUpload;
