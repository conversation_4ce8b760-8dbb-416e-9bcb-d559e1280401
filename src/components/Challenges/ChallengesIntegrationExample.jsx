import React from "react";
import { useTheme } from "@/components/ThemeProvider/ThemeProvider";
import Challenges from "./Challenges";

/**
 * Example of how to integrate the Challenges modal into different page layouts
 */

// Example 1: As a standalone centered component (current implementation)
export const ChallengesStandalone = ({ user, navigate, location }) => {
  return (
    <Challenges 
      user={user}
      navigate={navigate}
      location={location}
    />
  );
};

// Example 2: Integrated into a dashboard with other components
export const ChallengesDashboard = ({ user, navigate, location }) => {
  const { theme } = useTheme();

  return (
    <div className={`min-h-screen p-6 ${
      theme === 'dark' ? 'bg-gray-900' : 'bg-gray-100'
    }`}>
      <div className="max-w-7xl mx-auto">
        <h1 className={`text-3xl font-bold mb-8 ${
          theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>
          Dashboard
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Other dashboard components */}
          <div className={`p-6 rounded-lg ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          }`}>
            <h2 className={`text-xl font-semibold mb-4 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Quick Stats
            </h2>
            <p className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}>
              Some dashboard content here...
            </p>
          </div>
          
          <div className={`p-6 rounded-lg ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          }`}>
            <h2 className={`text-xl font-semibold mb-4 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Recent Activity
            </h2>
            <p className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}>
              Activity content here...
            </p>
          </div>
        </div>
        
        {/* Challenges Modal Button */}
        <div className="flex justify-center">
          <Challenges 
            user={user}
            navigate={navigate}
            location={location}
          />
        </div>
      </div>
    </div>
  );
};

// Example 3: Integrated into a navigation bar or header
export const ChallengesInHeader = ({ user, navigate, location }) => {
  const { theme } = useTheme();

  return (
    <header className={`p-4 border-b ${
      theme === 'dark' 
        ? 'bg-gray-800 border-gray-700' 
        : 'bg-white border-gray-200'
    }`}>
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <h1 className={`text-2xl font-bold ${
          theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>
          My App
        </h1>
        
        <div className="flex items-center space-x-4">
          {/* Other header items */}
          <nav className="flex space-x-4">
            <a href="#" className={`hover:underline ${
              theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
            }`}>
              Home
            </a>
            <a href="#" className={`hover:underline ${
              theme === 'dark' ? 'text-gray-300' : 'text-gray-600'
            }`}>
              About
            </a>
          </nav>
          
          {/* Challenges Modal Button - smaller size for header */}
          <div className="scale-75">
            <Challenges 
              user={user}
              navigate={navigate}
              location={location}
            />
          </div>
        </div>
      </div>
    </header>
  );
};

export default {
  ChallengesStandalone,
  ChallengesDashboard,
  ChallengesInHeader
};
