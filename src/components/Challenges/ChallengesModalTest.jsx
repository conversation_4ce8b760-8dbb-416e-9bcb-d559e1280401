import React from "react";
import { useTheme } from "@/components/ThemeProvider/ThemeProvider";
import Challenges from "./Challenges";

// Mock user data for testing
const mockUser = {
  client: {
    challengesTabName: "Test Challenges",
    challengesTabVisibility: true,
  }
};

// Mock functions for testing
const mockNavigate = (path) => console.log("Navigate to:", path);
const mockLocation = { pathname: "/challenges" };

const ChallengesModalTest = () => {
  const { theme } = useTheme();

  return (
    <div className={`min-h-screen p-8 ${
      theme === 'dark' 
        ? 'bg-gray-900 text-white' 
        : 'bg-gray-100 text-gray-900'
    }`}>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          Challenges Modal Test
        </h1>
        <p className="text-center mb-8 text-gray-600">
          Click the button below to open the Challenges modal
        </p>
        
        <Challenges 
          user={mockUser}
          navigate={mockNavigate}
          location={mockLocation}
        />
      </div>
    </div>
  );
};

export default ChallengesModalTest;
