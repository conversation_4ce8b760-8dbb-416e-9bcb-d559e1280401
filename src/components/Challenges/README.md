# Challenges Modal Component

The Challenges component has been converted from a full-page component to a modal-based component.

## Features

- **Modal Interface**: Opens in a centered modal dialog instead of taking up the full page
- **Trigger Button**: Displays a centered button that opens the modal when clicked
- **Responsive Design**: <PERSON><PERSON> adapts to different screen sizes with max-width of 6xl
- **Theme Support**: Fully supports both dark and light themes
- **Lazy Loading**: Only fetches challenge data when the modal is opened
- **Visibility Control**: Respects the `challengesTabVisibility` setting from the client

## Usage

```jsx
import Challenges from './components/Challenges/Challenges';

// Use in your component
<Challenges 
  user={user}
  navigate={navigate}
  location={location}
/>
```

## Props

- `user`: User object containing client configuration
- `navigate`: Navigation function (from React Router)
- `location`: Location object (from React Router)

## Modal Behavior

1. **Trigger**: A large, centered button displays the challenge tab name or "Open Challenges"
2. **Modal Size**: Maximum width of 6xl (72rem) and maximum height of 90vh
3. **Scrolling**: Content scrolls within the modal if it exceeds the available space
4. **Close**: <PERSON><PERSON> can be closed using the X button or by clicking outside

## Styling

The modal uses:
- Backdrop blur effect for the modal background
- Theme-aware colors (dark/light mode)
- Responsive layout that maintains the original two-column design (metrics + challenges)
- All original challenge card styling and animations

## Client Configuration

The component respects the following client settings:
- `challengesTabName`: Custom name for the challenges section
- `challengesTabVisibility`: Whether to show the challenges component at all

If `challengesTabVisibility` is false, the component returns null and doesn't render anything.
