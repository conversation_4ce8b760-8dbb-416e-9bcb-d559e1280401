import React, { Component } from 'react';
import {
  PageHeader,
  ControlLabel,
  Row,
  Col,
  FormControl,
  FormGroup,
  Button,
  Tabs,
  Tab,
  Checkbox,
} from 'react-bootstrap';
import 'react-rangeslider/lib/index.css';
import BlockUi from 'react-block-ui';
import { reduxForm, Field } from 'redux-form';

import { renderField } from '../App/RenderField';
import { addChallenge } from '../../actions/challenge';
import { getChallengeGroups } from '../../actions/challengeGroups';
import ChallengeImageUpload from './ChallengeImageUpload';

const validate = (values) => {
  const errors = {};
  let hasErrors = false;
  if (!values.name || values.name.trim() === '') {
    console.log('name validation error');
    errors.name = 'Enter a Scheme name';
    hasErrors = true;
  }

  // Validate alt challenge relationships
  if (values.challenges) {
    values.challenges.forEach((challenge, index) => {
      if (challenge.isAltChallenge && (!challenge.altOfChallenge || challenge.altOfChallenge === null)) {
        console.log(`Alt challenge validation error for challenge ${index + 1}`);
        errors[`challenge_${index}_altOfChallenge`] = `Challenge ${index + 1} is marked as alt challenge but no parent challenge is selected`;
        hasErrors = true;
      }

      // Prevent circular references - check if altOfChallenge matches current challenge's ID
      const currentChallengeId = challenge.id || index + 1;
      if (challenge.altOfChallenge === currentChallengeId) {
        console.log(`Circular reference error for challenge ${index + 1}`);
        errors[`challenge_${index}_circular`] = `Challenge ${index + 1} cannot be an alt version of itself`;
        hasErrors = true;
      }
    });
  }

  return hasErrors && errors;
};

class AddChallenge extends Component {
  constructor(props) {
    super(props);
    this.state = {
      name: '',
      applyToAllTeams: false,
      blocking: false,
      groups: [],
      challenges: [
        {
          imageUrl: '',
          description: '',
          bubbleSize: 'medium',
          group_id: '',
          optionA: '',
          consequenceA: '',
          optionMetric1A: 0,
          optionMetric2A: 0,
          optionMetric3A: 0,
          optionB: '',
          consequenceB: '',
          optionMetric1B: 0,
          optionMetric2B: 0,
          optionMetric3B: 0,
          optionC: '',
          consequenceC: '',
          optionMetric1C: 0,
          optionMetric2C: 0,
          optionMetric3C: 0,
        },
      ],
    };
    this.handleInputChange = this.handleInputChange.bind(this);
    this.handleFormSubmit = this.handleFormSubmit.bind(this);
    this.handleImageUploaded = this.handleImageUploaded.bind(this);
    this.handleImageRemoved = this.handleImageRemoved.bind(this);
  }

  componentDidMount() {
    this.loadGroups();
  }

  loadGroups = async () => {
    try {
      // For new challenge schemes, load global groups (challenge_scheme_id = null)
      const response = await getChallengeGroups();
      // Filter to show only global groups (those without a specific scheme)
      const globalGroups = response.data ? response.data.filter(group => !group.challenge_scheme_id) : [];
      this.setState({ groups: globalGroups });
    } catch (err) {
      console.error('Error loading groups:', err);
    }
  };

  handleFormSubmit(event) {
    const { name, applyToAllTeams, challenges } = this.state;
    const { alert, navigate } = this.props;

    event.preventDefault();
    this.blockUi();
    console.log('submitted', this.state);

    // Convert group_id from string to integer and remove group_name for each challenge
    const processedChallenges = challenges.map(challenge => {
      const { group_name, ...cleanChallenge } = challenge;
      return {
        ...cleanChallenge,
        group_id: challenge.group_id && challenge.group_id !== '' ? parseInt(challenge.group_id, 10) : null
      };
    });

    const body = {
      name,
      applyToAllTeams,
      challenges: processedChallenges,
    };
    addChallenge(body).payload.then(
      (response) => {
        this.unBlockUi();
        alert('success', 'Success', 'Challenge Scheme created successfully');

        navigate('/challenges');

        console.log('api response:', response);
      },
      (error) => {
        console.log(error);
        alert('danger', 'Error', `Challenge Scheme not created: ${error.toString()}`);
        this.unBlockUi();
      }
    );
  }

  handleInputChange(event) {
    const { target } = event;
    const value = target.type === 'checkbox' ? target.checked : target.value;
    const { name } = target;
    console.log('setting: ', [name], value, this.state);
    this.setState({
      [name]: value,
    });
  }

  handleChallengeChange(index, key, event) {
    const { challenges } = this.state;
    // Handle checkbox inputs differently from text/select inputs
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;

    // Convert altOfChallenge to integer if it's a number string, or null if empty
    if (key === 'altOfChallenge') {
      challenges[index][key] = value === '' ? null : parseInt(value, 10);
    } else {
      challenges[index][key] = value;
    }

    // If isAltChallenge is unchecked, clear altOfChallenge
    if (key === 'isAltChallenge' && !value) {
      challenges[index].altOfChallenge = null;
    }

    this.setState({
      challenges,
    });
    console.log(this.state);
  }

  blockUi() {
    this.setState({
      blocking: true,
    });
  }

  unBlockUi() {
    this.setState({
      blocking: false,
    });
  }

  addChallenge() {
    console.log('adding challenge');
    const { challenges } = this.state;
    if (challenges.length < 15) {
      challenges.push({
        imageUrl: '',
        description: '',
        bubbleSize: 'medium',
        group_id: '',
        optionA: '',
        consequenceA: '',
        optionMetric1A: 0,
        optionMetric2A: 0,
        optionMetric3A: 0,
        optionB: '',
        consequenceB: '',
        optionMetric1B: 0,
        optionMetric2B: 0,
        optionMetric3B: 0,
        optionC: '',
        consequenceC: '',
        optionMetric1C: 0,
        optionMetric2C: 0,
        optionMetric3C: 0,
        optionAIsAlt: false,
        optionBIsAlt: false,
        optionCIsAlt: false,
        isAltChallenge: false,
        altOfChallenge: null,
      });
      this.setState(
        {
          challenges,
        },
        () => {
          console.log(this.state);
        }
      );
    } else {
      alert('Only 25 challenges can be added');
    }
  }



  removeChallenge(index) {
    if (window.confirm('Are you sure that you want to delete this challenge ?')) {
      console.log('removing: ', index);
      const { challenges } = this.state;
      challenges.splice(index, 1);
      this.setState(
        {
          challenges,
        },
        () => {
          console.log(this.state);
        }
      );
    }
  }

  handleImageUploaded(challengeIndex, imageUrl) {
    const { challenges } = this.state;
    challenges[challengeIndex].imageUrl = imageUrl;
    this.setState({
      challenges,
    });
  }

  handleImageRemoved(challengeIndex) {
    const { challenges } = this.state;
    challenges[challengeIndex].imageUrl = '';
    this.setState({
      challenges,
    });
  }

  renderChallengesForm() {
    const { challenges } = this.state;

    return (
      <Tabs defaultActiveKey={0} id="tabs-container">
        {challenges.map((challenge, i) => {
          // Create tab title with alt challenge indicators
          let tabTitle = `Challenge #${i + 1}`;
          if (challenge.isAltChallenge && challenge.altOfChallenge) {
            // Find the parent challenge by ID to show its position
            const parentIndex = challenges.findIndex(c => (c.id || challenges.indexOf(c) + 1) === challenge.altOfChallenge);
            const parentNumber = parentIndex !== -1 ? parentIndex + 1 : challenge.altOfChallenge;
            tabTitle += ` (Alt of #${parentNumber})`;
          }
          if (challenge.optionAIsAlt || challenge.optionBIsAlt || challenge.optionCIsAlt) {
            tabTitle += ' 🔀'; // Add branching icon for challenges with alt options
          }

          return (
          // eslint-disable-next-line react/no-array-index-key
          <Tab eventKey={i} title={tabTitle} key={i}>
            <Row>
              <Col md={12}>
                <FormControl
                  componentClass="textarea"
                  value={challenges[i].description}
                  rows={5}
                  onChange={(e) => this.handleChallengeChange(i, 'description', e)}
                  placeholder="Enter description text"
                />
                <hr />
              </Col>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Bubble Size</ControlLabel>
                  <FormControl
                    componentClass="select"
                    value={challenges[i].bubbleSize || 'medium'}
                    onChange={(e) => this.handleChallengeChange(i, 'bubbleSize', e)}
                  >
                    <option value="small">Small</option>
                    <option value="medium">Medium</option>
                    <option value="large">Large</option>
                  </FormControl>
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Group (Optional)</ControlLabel>
                  <FormControl
                    componentClass="select"
                    value={challenges[i].group_id || ''}
                    onChange={(e) => this.handleChallengeChange(i, 'group_id', e)}
                  >
                    <option value="">Select a group (optional)</option>
                    {this.state.groups.map(group => (
                      <option key={group.id} value={group.id}>
                        {group.name} (Global)
                      </option>
                    ))}
                  </FormControl>
                  <div style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>
                    Only global groups are available when creating new schemes. Create scheme-specific groups after saving.
                  </div>
                </FormGroup>
                <hr />
              </Col>
              <Col md={12}>
                <ChallengeImageUpload
                  challengeIndex={i}
                  currentImageUrl={challenges[i].imageUrl}
                  onImageUploaded={this.handleImageUploaded}
                  onImageRemoved={this.handleImageRemoved}
                  alert={this.props.alert}
                />
                <hr />
              </Col>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Alt Challenge Settings</ControlLabel>
                  <div style={{ marginTop: '10px', marginBottom: '15px' }}>
                    <Checkbox
                      checked={challenges[i].isAltChallenge || false}
                      onChange={(e) => this.handleChallengeChange(i, 'isAltChallenge', e)}
                    >
                      This is an alt challenge
                    </Checkbox>
                  </div>
                  {challenges[i].isAltChallenge && (
                    <div style={{ marginBottom: '15px' }}>
                      <ControlLabel>Alt version of challenge</ControlLabel>
                      <FormControl
                        componentClass="select"
                        value={challenges[i].altOfChallenge || ''}
                        onChange={(e) => this.handleChallengeChange(i, 'altOfChallenge', e)}
                      >
                        <option value="">Select challenge...</option>
                        {challenges.map((challenge, idx) => {
                          // Don't show current challenge
                          if (idx === i) return null;
                          return (
                            <option key={idx} value={challenge.id || idx + 1}>
                              Challenge #{idx + 1} {challenge.description ? `- ${challenge.description.substring(0, 50)}...` : ''}
                            </option>
                          );
                        })}
                      </FormControl>
                    </div>
                  )}
                </FormGroup>
                <hr />
              </Col>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Option A)</ControlLabel>
                  <FormControl
                    type="text"
                    className="margin-bottom-20"
                    placeholder="Enter option name"
                    value={challenges[i].optionA}
                    name="name"
                    onChange={(e) => this.handleChallengeChange(i, 'optionA', e)}
                  />
                  <ControlLabel>Consequence A)</ControlLabel>
                  <FormControl
                    componentClass="textarea"
                    name="description"
                    placeholder="Enter description text"
                    value={challenges[i].consequenceA}
                    onChange={(e) => this.handleChallengeChange(i, 'consequenceA', e)}
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 1 A</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric1A"
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric1A', e)}
                    placeholder="Enter metric 1 A value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 2 A</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric2A"
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric2A', e)}
                    placeholder="Enter metric 2 A value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 3 A</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric3A"
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric3A', e)}
                    placeholder="Enter metric 3 A value"
                  />
                </FormGroup>
                <FormGroup>
                  <Checkbox
                    checked={challenges[i].optionAIsAlt || false}
                    onChange={(e) => this.handleChallengeChange(i, 'optionAIsAlt', e)}
                  >
                    Option A leads to alt challenge
                  </Checkbox>
                </FormGroup>
                <hr />
              </Col>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Option B)</ControlLabel>
                  <FormControl
                    type="text"
                    className="margin-bottom-20"
                    placeholder="Enter option name"
                    value={challenges[i].optionB}
                    onChange={(e) => this.handleChallengeChange(i, 'optionB', e)}
                  />
                  <ControlLabel>Consequence B)</ControlLabel>
                  <FormControl
                    componentClass="textarea"
                    placeholder="Enter description text"
                    value={challenges[i].consequenceB}
                    onChange={(e) => this.handleChallengeChange(i, 'consequenceB', e)}
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 1 B</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric1B"
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric1B', e)}
                    placeholder="Enter metric 1 B value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 2 B</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric2B"
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric2B', e)}
                    placeholder="Enter metric 2 B value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 3 B</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric3B"
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric3B', e)}
                    placeholder="Enter metric 3 B value"
                  />
                </FormGroup>
                <FormGroup>
                  <Checkbox
                    checked={challenges[i].optionBIsAlt || false}
                    onChange={(e) => this.handleChallengeChange(i, 'optionBIsAlt', e)}
                  >
                    Option B leads to alt challenge
                  </Checkbox>
                </FormGroup>
                <hr />
              </Col>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Option C)</ControlLabel>
                  <FormControl
                    type="text"
                    className="margin-bottom-20"
                    placeholder="Enter option name"
                    value={challenges[i].optionC}
                    onChange={(e) => this.handleChallengeChange(i, 'optionC', e)}
                  />
                  <ControlLabel>Consequence C)</ControlLabel>
                  <FormControl
                    componentClass="textarea"
                    placeholder="Enter description text"
                    value={challenges[i].consequenceC}
                    onChange={(e) => this.handleChallengeChange(i, 'consequenceC', e)}
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 1 C</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric1C"
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric1C', e)}
                    placeholder="Enter metric 1 C value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 2 C</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric2C"
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric2C', e)}
                    placeholder="Enter metric 2 C value"
                  />
                </FormGroup>
                <FormGroup>
                  <ControlLabel>Metric 3 C</ControlLabel>
                  <FormControl
                    type="number"
                    name="optionMetric3C"
                    onChange={(e) => this.handleChallengeChange(i, 'optionMetric3C', e)}
                    placeholder="Enter metric 3 C value"
                  />
                </FormGroup>
                <FormGroup>
                  <Checkbox
                    checked={challenges[i].optionCIsAlt || false}
                    onChange={(e) => this.handleChallengeChange(i, 'optionCIsAlt', e)}
                  >
                    Option C leads to alt challenge
                  </Checkbox>
                </FormGroup>
                <hr />
              </Col>
            </Row>
            <Row>
              <Col md={12} onClick={() => this.removeChallenge(i)}>
                <Button>Delete This Challenge</Button>
              </Col>
            </Row>
          </Tab>
          );
        })}
      </Tabs>
    );
  }

  render() {
    const { blocking } = this.state;

    const horizontalLabels = {};
    for (let i = 1; i < 16; i++) horizontalLabels[i] = i;

    return (
      <div className="container root-container">
        <BlockUi tag="div" blocking={blocking}>
          <Row>
            <Col md={10}>
              <PageHeader>Add Challenge Scheme</PageHeader>
            </Col>
            <Col md={2}>
              <Button
                className="pull-right btn-info save-button"
                onClick={(e) => this.handleFormSubmit(e)}
                disabled={!!validate(this.state)}
              >
                Save
              </Button>
            </Col>
          </Row>
          <form>
            <Row>
              <Col md={12}>
                <FormGroup>
                  <ControlLabel>Name</ControlLabel>
                  <Field
                    name="name"
                    type="text"
                    component={renderField}
                    onChange={this.handleInputChange}
                    label="Enter Challenge Scheme Name*"
                  />
                </FormGroup>
                <Checkbox value name="applyToAllTeams" onChange={(e) => this.handleInputChange(e)}>
                  Apply this scheme to all teams
                </Checkbox>

              </Col>
            </Row>
            <hr />
            <Row>
              <Col md={12}>
                <Button bsStyle="success" className="pull-right" onClick={() => this.addChallenge()}>
                  Add Challenge
                </Button>
              </Col>
            </Row>
            {this.renderChallengesForm()}
          </form>
        </BlockUi>
      </div>
    );
  }
}

export default reduxForm({
  form: 'AddChallenge',
  validate,
})(AddChallenge);
