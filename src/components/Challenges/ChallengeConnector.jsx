import React, { useEffect, useState } from 'react';
import { useTheme } from "@/components/ThemeProvider/ThemeProvider";

const ChallengeConnector = ({ challenges, circlePositions, containerRef }) => {
  const { theme } = useTheme();
  const [calculatedPositions, setCalculatedPositions] = useState([]);

  // Filter out alt challenges and get only main challenges in order
  const mainChallenges = challenges.filter(challenge => !challenge.isAltChallenge);

  /**
   * POSITION CALCULATION EFFECT:
   *
   * This effect runs whenever the challenges, circle positions, or container changes.
   * It's responsible for finding the actual screen coordinates of each challenge circle
   * so we can draw accurate connecting lines.
   *
   * The challenge: Circle positions are determined by:
   * 1. Flex layout positioning (automatic)
   * 2. Random CSS transform offsets (stored in circlePositions state)
   * 3. Different circle sizes (small/medium/large)
   *
   * Rather than trying to calculate these mathematically (which would be complex
   * and error-prone), we let the browser do the layout, then query the DOM to
   * find where each circle actually ended up on screen.
   */
  useEffect(() => {
    // Early exit conditions: need at least 2 circles and a valid container
    if (mainChallenges.length < 2 || !containerRef?.current) {
      setCalculatedPositions([]);
      return;
    }

    /**
     * Calculate the actual screen positions of all challenge circles
     *
     * This function finds each circle in the DOM and calculates its center position
     * relative to the container. This is necessary because:
     * 1. Circles have random offsets applied via CSS transforms
     * 2. Flex layout positioning is complex to calculate mathematically
     * 3. We need pixel-perfect accuracy for smooth line connections
     */
    const calculatePositions = () => {
      const container = containerRef.current;
      if (!container) return;

      // Get the container's position on screen as our reference point
      const containerRect = container.getBoundingClientRect();
      const positions = [];

      // Group challenges by their groupId to maintain proper ordering
      // This ensures we connect circles in the correct sequence across groups
      const groupedChallenges = mainChallenges.reduce((groups, challenge) => {
        const groupId = challenge.groupId || 0;
        if (!groups[groupId]) {
          groups[groupId] = [];
        }
        groups[groupId].push(challenge);
        return groups;
      }, {});

      // Sort groups by ID (lowest first) to ensure consistent ordering
      const sortedGroups = Object.keys(groupedChallenges)
        .sort((a, b) => parseInt(a) - parseInt(b))
        .map(groupId => groupedChallenges[groupId]);

      // Calculate the center position of each challenge circle in sequential order
      let globalIndex = 0;
      sortedGroups.forEach((group, groupIndex) => {
        group.forEach((challenge, challengeIndexInGroup) => {
          // Find the actual DOM element using the data-challenge-id attribute
          // This attribute is added to each circle div in the main component
          const challengeElement = container.querySelector(`[data-challenge-id="${challenge.id}"]`);

          if (challengeElement) {
            // Get the circle's position and size on screen
            const elementRect = challengeElement.getBoundingClientRect();

            // Calculate the center point of the circle
            // We subtract containerRect coordinates to get position relative to our SVG container
            const centerX = elementRect.left + elementRect.width / 2 - containerRect.left;
            const centerY = elementRect.top + elementRect.height / 2 - containerRect.top;

            // Store the calculated position for line drawing
            positions.push({
              id: challenge.id,
              x: centerX,
              y: centerY,
              groupId: challenge.groupId || 0,
              challengeIndex: globalIndex
            });
          }
          globalIndex++;
        });
      });

      // Only update state if we successfully found positions for ALL challenges
      // This prevents partial line drawing if some circles aren't rendered yet
      if (positions.length === mainChallenges.length) {
        setCalculatedPositions(positions);
      }
    };

    /**
     * TIMING STRATEGY:
     *
     * We use a 100ms delay before calculating positions because:
     * 1. React renders components asynchronously
     * 2. CSS transforms (the random offsets) are applied after initial render
     * 3. Flex layout calculations happen after DOM insertion
     *
     * The delay ensures all visual positioning is complete before we measure.
     */
    const timeoutId = setTimeout(calculatePositions, 100);

    /**
     * RESPONSIVE HANDLING:
     *
     * When the window resizes, the flex layout repositions circles,
     * so we need to recalculate their positions and redraw the lines.
     * We use the same 100ms delay to ensure layout stabilizes after resize.
     */
    const handleResize = () => {
      setTimeout(calculatePositions, 100);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup: Remove event listeners and cancel pending timeouts when component unmounts
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
    };
  }, [challenges, circlePositions, containerRef]); // Re-run when any of these dependencies change

  /**
   * SIMPLE STRAIGHT LINE PATH GENERATION
   *
   * Creates straight lines connecting each circle center to the next.
   * Simple and clean - no curves, just direct connections.
   *
   * @param {Array} positions - Array of {x, y, id} objects representing circle centers
   * @returns {string} SVG path string with straight line connections
   */
  const generateStraightPath = (positions) => {
    if (positions.length < 2) return '';

    // Start the SVG path by moving to the first circle's center
    let path = `M ${positions[0].x} ${positions[0].y}`;

    // Draw straight lines to each subsequent circle
    for (let i = 1; i < positions.length; i++) {
      const current = positions[i];
      path += ` L ${current.x} ${current.y}`;
    }

    return path;
  };

  const pathData = generateStraightPath(calculatedPositions);

  if (!pathData || calculatedPositions.length < 2) return null;

  return (
    <svg
      className="absolute inset-0 pointer-events-none"
      style={{ zIndex: 1 }}
      width="100%"
      height="100%"
    >
      {/* SVG Definitions - Reusable elements like filters and markers */}
      <defs>
        {/*
          Glow Filter Effect:
          Creates a soft glow around the line by:
          1. Blurring a copy of the line (feGaussianBlur)
          2. Layering the blurred copy behind the original (feMerge)
          This gives the line a subtle luminous appearance
        */}
        <filter id="challengeGlow">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>    {/* Blurred background layer */}
            <feMergeNode in="SourceGraphic"/>  {/* Original sharp line on top */}
          </feMerge>
        </filter>

        {/*
          Arrow Marker:
          Creates a small triangular arrowhead that can be placed at the end of paths
          - markerWidth/Height: Size of the marker viewport
          - refX/refY: Where the marker attaches to the path (tip of arrow)
          - orient="auto": Arrow automatically rotates to match path direction
        */}
        <marker
          id="arrowhead"
          markerWidth="10"
          markerHeight="7"
          refX="9"        // Position tip of arrow at path end
          refY="3.5"      // Center arrow vertically
          orient="auto"   // Auto-rotate to match path direction
        >
          <polygon
            points="0 0, 10 3.5, 0 7"  // Triangle shape: left-top, right-center, left-bottom
            fill={theme === 'dark' ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.3)'}
          />
        </marker>
      </defs>

      {/*
        MAIN CURVED PATH:
        This is the primary visible line connecting all circles
        - d={pathData}: The path string generated by generateCurvedPath()
        - fill="none": We only want the stroke (outline), not a filled shape
        - stroke: Line color that adapts to theme
        - strokeWidth: Line thickness in pixels
        - filter: Applies the glow effect defined above
        - markerEnd: Places an arrow at the end of the path
      */}
      <path
        d={pathData}
        fill="none"
        stroke={theme === 'dark' ? 'rgba(255, 255, 255, 0.25)' : 'rgba(0, 0, 0, 0.15)'}
        strokeWidth="3"
        filter="url(#challengeGlow)"
        markerEnd="url(#arrowhead)"
      />

      {/*
        GLOW EFFECT LAYER:
        A thicker, more transparent version of the same path rendered underneath
        This creates a subtle "aura" effect around the main line
        - Same path data but thicker (strokeWidth="4") and more transparent
        - Blue tint adds a subtle color accent
        - No filter applied to avoid double-glow effect
      */}
      <path
        d={pathData}
        fill="none"
        stroke={theme === 'dark' ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'}
        strokeWidth="4"
        opacity="0.5"
      />

      {/*
        CONNECTION POINT INDICATORS:
        Small circles placed at each challenge circle's center
        These provide visual confirmation of where the line connects
        - cx/cy: Circle center coordinates
        - r: Radius in pixels
        - animate-pulse: CSS animation for subtle breathing effect
      */}
      {calculatedPositions.map((position, index) => (
        <circle
          key={`marker-${position.id}`}
          cx={position.x}
          cy={position.y}
          r="2"
          fill={theme === 'dark' ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.2)'}
          className="animate-pulse"
        />
      ))}
    </svg>
  );
};

export default ChallengeConnector;
