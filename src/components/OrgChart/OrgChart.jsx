import React, { useState, useEffect, useMemo, useRef } from "react";
import { useTheme } from "@/components/ThemeProvider/ThemeProvider";
import { Tree, TreeNode } from "react-organizational-chart";
import { Lock, HelpCircle, MinusCircle, PlusCircle } from "lucide-react";
import { toast } from "sonner";
import to from "await-to-js";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog-themed";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Loader from "../common/Loader";
import { getOrgChart, saveOrgChart } from "../../actions/user";
import styles from "./org-chart.module.scss";

// Sub-components
const OrgChartLegend = () => {
  const { theme } = useTheme();

  const legendItems = [
    { id: 1, name: "Supporter", color: "#00CC66" },
    { id: 2, name: "Neutral", color: "#FFCB06" },
    { id: 3, name: "Non-Supporter", color: "#FA582D" },
    { id: 4, name: "Unknown", color: "grey" },
    { id: 5, name: "Locked", color: "black" },
  ];

  return (
    <div className={styles.legend}>
      {legendItems.map((item) => (
        <div key={item.id} className={styles.legendItem}>
          <span
            className={styles.legendColor}
            style={{ backgroundColor: item.color }}
          />
          <span className={styles.legendText}>{item.name}</span>
        </div>
      ))}
    </div>
  );
};

const ZoomControls = ({ onZoom }) => {
  const { theme } = useTheme();
  return (
  <div className={styles.zoomControls}>
    <button
      onClick={() => onZoom("reset")}
      className={styles.resetButton}
    >
      Reset zoom
    </button>
    <button
      onClick={() => onZoom("out")}
      className={styles.zoomButton}
    >
      <MinusCircle className="w-5 h-5" />
    </button>
    <button
      onClick={() => onZoom("in")}
      className={styles.zoomButton}
    >
      <PlusCircle className="w-5 h-5" />
    </button>
  </div>
  );
};

const FilterButton = ({ active, children, onClick }) => {
  const { theme } = useTheme();
  return (
  <button
    onClick={onClick}
    className={`${styles.filterButton} ${active ? styles.active : ''}`}
  >
    {children}
  </button>
  );
};

const OrgChartNode = ({
  type = "global",
  nodeData,
  meetingPoints,
  users = [],
  onUpdate = () => {},
}) => {
  const { theme } = useTheme();
  const [showMeetingModal, setShowMeetingModal] = useState(false);
  const { name, title, status, locked_by_id: lockedById, photo } = nodeData;

  const isLockedUser = useMemo(() => {
    if (!lockedById) return false;
    return users.some((u) => u.id === lockedById && !u.meet_1);
  }, [lockedById, users]);

  const getStatusClasses = () => {
    if (isLockedUser) return theme === 'dark' ? "bg-black/50" : "bg-gray-200/70";
    if (status <= -1) return theme === 'dark' ? "bg-[#FA582D]/50" : "bg-[#FA582D]/20";
    if (status === 1) return theme === 'dark' ? "bg-[#FFCB06]/50" : "bg-[#FFCB06]/20";
    if (status >= 2) return theme === 'dark' ? "bg-[#00CC66]/50" : "bg-[#00CC66]/20";
    return theme === 'dark' ? "bg-gray-500/50" : "bg-gray-300/50";
  };

  return (
    <>
      <div
        onClick={() => !isLockedUser && setShowMeetingModal(true)}
        className={`
          ${styles.node}
          ${type === "internal" ? styles.nodeInternal : styles.nodeGlobal}
          ${getStatusClasses()}
        `}
      >
        {isLockedUser && (
          <div className={styles.nodeLocked}>
            <Lock className="w-6 h-6 text-white/50" />
          </div>
        )}

        {photo ? (
          <div
            className={styles.nodePhoto}
            style={{ backgroundImage: `url(${photo})` }}
          />
        ) : (
          <div className={styles.nodePhotoPlaceholder}>
            Photo
          </div>
        )}

        <div className={styles.nodeContent}>
          <h5 className={styles.nodeName} title={name}>
            {name}
          </h5>
          <h4 className={styles.nodeTitle} title={title}>
            {title}
          </h4>
        </div>
      </div>

      {showMeetingModal && (
        <Dialog open onOpenChange={() => setShowMeetingModal(false)}>
          <DialogContent className={`backdrop-blur-xl ${theme === 'dark' ? 'bg-black/40 border border-white/10 text-white' : 'bg-white/80 border border-gray-300/30 text-gray-800'}`}>
            <DialogHeader>
              <DialogTitle className="text-center space-y-2">
                <div className={`text-xl ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Meet with {name}</div>
                {title && (
                  <div className={`text-sm font-normal ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                    {title}
                  </div>
                )}
              </DialogTitle>
            </DialogHeader>
            <OrgChartMeetModal
              {...nodeData}
              meetingPoints={meetingPoints}
              onUpdate={onUpdate}
              onClose={() => setShowMeetingModal(false)}
            />
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

const MeetingContent = ({ label, text, time }) => {
  const { theme } = useTheme();
  return (
    <div className="space-y-2">
      {label && (
        <Label className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>{label}</Label>
      )}
      <div className={`p-4 backdrop-blur-xl rounded-lg ${theme === 'dark' ? 'bg-black/40 border border-white/10' : 'bg-white/60 border border-gray-300/30'}`}>
        {time && (
          <div className={`text-sm mb-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Meeting time: {time}
          </div>
        )}
        <p className={`leading-relaxed whitespace-pre-wrap ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>
          {text}
        </p>
      </div>
    </div>
  );
};

const OrgChartMeetModal = ({
  id,
  name,
  title,
  meetingPoints,
  meet_1: isMeeting1Finished,
  meet_2: isMeeting2Finished,
  meet_3: isMeeting3Finished,
  meet_1_text: meeting1,
  meet_1_points: meeting1Points,
  meet_2_text: meeting2,
  meet_2_points: meeting2Points,
  meet_3_text: meeting3,
  meet_3_points: meeting3Points,
  onUpdate = () => {},
  onClose = () => {},
}) => {
  const { theme } = useTheme();
  const [step, setStep] = useState(0);
  const hasMeetings = useMemo(() => meeting1, [meeting1]);

  const MeetingCost = ({ cost }) => {
    return (
      <div className="text-center space-y-3 py-4">
        <p className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}>This meeting will cost</p>
        <p className={`text-3xl font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{cost} time units</p>
        <p className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}>Would you like to proceed?</p>
      </div>
    );
  };

  const MeetingContent = ({ label, text }) => {
    return (
      <div className="space-y-2">
        {label && (
          <Label className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>{label}</Label>
        )}
        <div className={`p-4 rounded-lg ${theme === 'dark' ? 'bg-white/5 border border-white/10' : 'bg-white/60 border border-gray-300/30'}`}>
          <p className={`leading-relaxed whitespace-pre-wrap ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>
            {text}
          </p>
        </div>
      </div>
    );
  };

  const renderModalContent = () => {
    if (!hasMeetings) {
      return (
        <>
          <div className={`text-center py-6 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
            Sorry, <span className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{name}</span> does
            not have any meetings.
          </div>
          <DialogFooter>
            <Button onClick={onClose}>Got it</Button>
          </DialogFooter>
        </>
      );
    }

    if (
      !isMeeting1Finished ||
      (isMeeting1Finished && !isMeeting2Finished && step === 1)
    ) {
      if (step === 0) {
        return (
          <>
            <MeetingCost cost={meeting1Points} />
            <DialogFooter className="sm:justify-between">
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  onUpdate({ userId: id, key: "meet_1", value: true });
                  setStep(1);
                }}
                disabled={meeting1Points > meetingPoints}
              >
                Proceed
              </Button>
            </DialogFooter>
          </>
        );
      }
      return (
        <>
          <MeetingContent text={meeting1} />
          <DialogFooter>
            <Button onClick={onClose}>Done</Button>
          </DialogFooter>
        </>
      );
    }

    if (
      meeting2 &&
      (!isMeeting2Finished ||
        (isMeeting2Finished && !isMeeting3Finished && step === 1))
    ) {
      if (step === 0) {
        return (
          <>
            <ScrollArea className="max-h-[60vh]">
              <div className="space-y-4 py-4">
                <MeetingContent label="1st Meeting" text={meeting1} />
                <MeetingCost cost={meeting2Points} />
              </div>
            </ScrollArea>
            <DialogFooter className="sm:justify-between">
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  onUpdate({ userId: id, key: "meet_2", value: true });
                  setStep(1);
                }}
                disabled={meeting2Points > meetingPoints}
              >
                Proceed
              </Button>
            </DialogFooter>
          </>
        );
      }
      return (
        <>
          <MeetingContent text={meeting2} />
          <DialogFooter>
            <Button onClick={onClose}>Done</Button>
          </DialogFooter>
        </>
      );
    }

    if (
      meeting3 &&
      (!isMeeting3Finished || (isMeeting3Finished && step === 1))
    ) {
      if (step === 0) {
        return (
          <>
            <ScrollArea className="max-h-[60vh]">
              <div className="space-y-4 py-4">
                <MeetingContent label="1st Meeting" text={meeting1} />
                <MeetingContent label="2nd Meeting" text={meeting2} />
                <MeetingCost cost={meeting3Points} />
              </div>
            </ScrollArea>
            <DialogFooter className="sm:justify-between">
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  onUpdate({ userId: id, key: "meet_3", value: true });
                  setStep(1);
                }}
                disabled={meeting3Points > meetingPoints}
              >
                Proceed
              </Button>
            </DialogFooter>
          </>
        );
      }
      return (
        <>
          <MeetingContent text={meeting3} />
          <DialogFooter>
            <Button onClick={onClose}>Done</Button>
          </DialogFooter>
        </>
      );
    }

    return (
      <>
        <ScrollArea className="max-h-[60vh]">
          <div className="space-y-4 py-4">
            <MeetingContent label="1st Meeting" text={meeting1} />
            {meeting2 && <MeetingContent label="2nd Meeting" text={meeting2} />}
            {meeting3 && <MeetingContent label="3rd Meeting" text={meeting3} />}
          </div>
        </ScrollArea>
        <DialogFooter>
          <Button onClick={onClose}>Done</Button>
        </DialogFooter>
      </>
    );
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle className="text-center space-y-2">
            <div className="text-xl">Meet with {name}</div>
            {title && (
              <div className="text-sm font-normal text-gray-400">{title}</div>
            )}
          </DialogTitle>
        </DialogHeader>
        {renderModalContent()}
      </DialogContent>
    </Dialog>
  );
};

// New recursive TreeNode renderer
const RecursiveTreeNode = ({ data, users, meetingPoints, onUpdate }) => {
  return (
    <TreeNode
      label={
        <div className="mx-auto w-fit">
          <OrgChartNode
            nodeData={data}
            users={users}
            meetingPoints={meetingPoints}
            onUpdate={onUpdate}
          />
        </div>
      }
    >
      {data.children?.map((child) => (
        <RecursiveTreeNode
          key={child.id}
          data={child}
          users={users}
          meetingPoints={meetingPoints}
          onUpdate={onUpdate}
        />
      ))}
    </TreeNode>
  );
};

// Main OrgChart Component
const OrgChart = ({ user, navigate }) => {
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [orgCharts, setOrgCharts] = useState([]);
  const [selectedOrgChartId, setSelectedOrgChartId] = useState(-1);
  const [meetingPoints, setMeetingPoints] = useState(0);
  const [zoom, setZoom] = useState(0.8);

  const treeContainerRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [treePosition, setTreePosition] = useState({ x: 0, y: 0 });

  const handleZoom = (action) => {
    switch (action) {
      case "in":
        setZoom((prev) => Math.min(prev + 0.2, 2));
        break;
      case "out":
        setZoom((prev) => Math.max(prev - 0.2, 0.4));
        break;
      case "reset":
        setZoom(1);
        break;
    }
  };

  const handleMouseDown = (e) => {
    setIsDragging(true);
    setDragStart({ x: e.clientX - treePosition.x, y: e.clientY - treePosition.y });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    setTreePosition({ x: e.clientX - dragStart.x, y: e.clientY - dragStart.y });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging]);

  const { orgChartTabName, lightHighlightColor } = user?.client || {};

  useEffect(() => {
    fetchOrgChart();
  }, []);

  const fetchOrgChart = async () => {
    setIsLoading(true);
    const [err, res] = await to(getOrgChart().payload);

    if (err) {
      toast.error("Failed to load org chart");
      setIsLoading(false);
      return;
    }

    if (res.data.orgCharts?.length) {
      setOrgCharts(res.data.orgCharts);
      setSelectedOrgChartId(res.data.orgCharts[0].id);
    }
    setMeetingPoints(res.data.meetingPoints);
    setIsLoading(false);
  };

  const handleUpdate = async (orgChartType, { userId, key, value, meetingTime }) => {
    setOrgCharts((prev) => {
      const updatedOrgCharts = prev.map((orgChart) => {
        if (orgChart.id !== selectedOrgChartId) return orgChart;

        const updatedTypes = orgChart.orgChartTypes.map((type) => {
          if (type.id !== orgChartType.id) return type;

          const updatedUsers = type.users.map((user) => {
            if (user.id !== userId) return user;

            if (key.startsWith("meet_")) {
              const points = parseInt(user[`${key}_points`], 10);
              setMeetingPoints((prev) => prev - points);
              user.status++;
            }

            return {
              ...user,
              [key]: value,
              [`${key}_time`]: new Date(),
              [`${key}_selected_time`]: meetingTime
            };
          });

          return { ...type, users: updatedUsers };
        });

        return { ...orgChart, orgChartTypes: updatedTypes };
      });

      // Save to backend
      saveOrgChart(updatedOrgCharts)
        .payload.then(() => toast.success("Meeting was recorded!"))
        .catch(() => toast.error("Failed to save meeting"));

      return updatedOrgCharts;
    });
  };

  const selectedOrgChart = useMemo(
    () => orgCharts.find((chart) => chart.id === selectedOrgChartId),
    [orgCharts, selectedOrgChartId]
  );

  if (isLoading) return <Loader />;

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>
          {orgChartTabName || "Organization Chart"}
        </h2>
        <div className={styles.timeUnits}>
          Time Units: <span>{meetingPoints}</span>
        </div>
      </div>

      <div className={styles.controls}>
        <div className={styles.filterButtons}>
          {orgCharts.map((chart, index) => (
            <FilterButton
              key={chart.id}
              active={chart.id === selectedOrgChartId}
              onClick={() => setSelectedOrgChartId(chart.id)}
            >
              Org Chart #{index + 1}
            </FilterButton>
          ))}
        </div>
        <ZoomControls onZoom={handleZoom} />
      </div>

      {selectedOrgChart?.orgChartTypes?.map(
        (orgChartType) =>
          orgChartType.is_visible && (
            <div key={orgChartType.id} className="space-y-4">
              <div className={styles.chartTypeHeader}>
                <h3 className={styles.chartTypeTitle}>
                  {orgChartType.name}
                </h3>

                {orgChartType.type === "global" && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-gray-400 hover:text-white"
                        >
                          <HelpCircle className="h-5 w-5" />
                        </Button>
                      </TooltipTrigger>

                      <TooltipContent
                        side="left"
                        className="max-w-sm bg-black/40 backdrop-blur-xl border-white/10 text-white"
                      >
                        <p>
                          Click on a stakeholder to initiate a meeting. Each
                          meeting will take time units and some stakeholders
                          will take more than others. You will also have the
                          opportunity to meet with some stakeholders multiple
                          times.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>

              <div className={styles.chartContainer}>
                {orgChartType.type === "global" ? (
                  <div
                    className={styles.treeContainer}
                    ref={treeContainerRef}
                    onMouseDown={handleMouseDown}
                    style={{ cursor: isDragging ? "grabbing" : "grab" }}
                  >
                    <div
                      className={styles.treeContent}
                      style={{
                        transform: `translate(${treePosition.x}px, ${treePosition.y}px) scale(${zoom})`,
                        transition: isDragging ? "none" : "transform 0.2s ease-out",
                      }}
                    >
                      <div className={styles.treeInner}>
                        <Tree
                          lineWidth="2px"
                          lineColor={theme === 'dark' ? "rgba(255, 255, 255, 0.2)" : "rgba(100, 100, 100, 0.2)"}
                          lineBorderRadius="6px"
                          label={
                            <div className="mx-auto w-fit">
                              <OrgChartNode
                                nodeData={
                                  createTreeFromArray(orgChartType.users)[0]
                                }
                                users={orgChartType.users}
                                meetingPoints={meetingPoints}
                                onUpdate={(e) => handleUpdate(orgChartType, e)}
                              />
                            </div>
                          }
                        >
                          {createTreeFromArray(
                            orgChartType.users
                          )[0]?.children?.map((node) => (
                            <RecursiveTreeNode
                              className="text-center mx-auto"
                              key={node.id}
                              data={node}
                              users={orgChartType.users}
                              meetingPoints={meetingPoints}
                              onUpdate={(e) => handleUpdate(orgChartType, e)}
                            />
                          ))}
                        </Tree>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className={styles.gridContainer}>
                    {orgChartType.users.map((user) => (
                      <OrgChartNode
                        key={user.id}
                        type="internal"
                        nodeData={user}
                        users={orgChartType.users}
                        meetingPoints={meetingPoints}
                        onUpdate={(e) => handleUpdate(orgChartType, e)}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
          )
      )}

      <OrgChartLegend />
    </div>
  );
};

// Keep your existing createTreeFromArray helper function
const createTreeFromArray = (array) => {
  const map = {};
  const roots = [];

  array.forEach((item) => {
    map[item.id] = { ...item, children: [] };
  });

  array.forEach((item) => {
    const node = map[item.id];
    if (item.parent_id && map[item.parent_id]) {
      map[item.parent_id].children.push(node);
    } else {
      roots.push(node);
    }
  });

  return roots;
};

export default OrgChart;
