// OrgChart component styles
.container {
  min-height: 100%;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
}

.timeUnits {
  font-weight: 600;
}

.controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.filterButtons {
  display: flex;
  gap: 0.5rem;
}

.filterButton {
  padding: 0.25rem 1rem;
  border-radius: 9999px;
  transition: all 0.2s;
}

.zoomControls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.resetButton {
  background-color: #0095ff;
  color: white;
  padding: 0.25rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(0, 149, 255, 0.9);
  }
}

.zoomButton {
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  backdrop-filter: blur(8px);
}

.chartTypeHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chartTypeTitle {
  font-size: 1.125rem;
  font-weight: 600;
}

.chartContainer {
  border-radius: 0.75rem;
  backdrop-filter: blur(8px);
}

.treeContainer {
  height: 400px;
  position: relative;
  overflow: hidden;
  margin: 0 auto;
}

.treeContent {
  position: absolute;
  inset: 0;
  overflow: visible;
  padding: 20px;
  transform-origin: 50% 0%;
}

.treeInner {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gridContainer {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1rem;
  padding: 1rem;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.75rem;
  backdrop-filter: blur(8px);
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legendColor {
  width: 1rem;
  height: 1rem;
  border-radius: 9999px;
}

.legendText {
  font-size: 0.875rem;
}

// Node styles
.node {
  display: flex;
  align-items: center;
  backdrop-filter: blur(8px);
  border-radius: 0.75rem;
  transition: all 0.2s;
  cursor: pointer;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.nodeInternal {
  width: 16rem;
}

.nodeGlobal {
  width: fit-content;
}

.nodeLocked {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0.75rem;
}

.nodePhoto {
  width: 3rem;
  height: 3rem;
  margin: 5px;
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.nodePhotoPlaceholder {
  width: 3rem;
  height: 3rem;
  margin: 5px;
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nodeContent {
  padding: 0.75rem;
  flex: 1;
  min-width: 0;
}

.nodeName {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nodeTitle {
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Dark mode styles
:global(.dark) {
  .title, .chartTypeTitle {
    color: #fff;
  }

  .timeUnits {
    color: #fff;
  }

  .filterButton {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.4);
    color: rgba(255, 255, 255, 0.7);

    &:hover {
      background-color: rgba(255, 255, 255, 0.05);
    }

    &.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: #fff;
    }
  }

  .zoomButton {
    background-color: rgba(0, 0, 0, 0.4);
    color: #fff;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .chartContainer {
    background-color: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .legend {
    background-color: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .legendText {
    color: #e5e7eb;
  }

  .node {
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .nodePhotoPlaceholder {
    background-color: rgba(0, 0, 0, 0.4);
    color: rgba(255, 255, 255, 0.5);
  }

  .nodeName {
    color: #fff;
  }

  .nodeTitle {
    color: #d1d5db;
  }
}

// Light mode styles
:global(.light) {
  .title, .chartTypeTitle {
    color: #1f2937;
  }

  .timeUnits {
    color: #1f2937;
  }

  .filterButton {
    border: 1px solid rgba(209, 213, 219, 0.3);
    background-color: rgba(255, 255, 255, 0.3);
    color: #4b5563;

    &:hover {
      background-color: rgba(255, 255, 255, 0.5);
    }

    &.active {
      background-color: rgba(255, 255, 255, 0.7);
      color: #1f2937;
    }
  }

  .zoomButton {
    background-color: rgba(255, 255, 255, 0.4);
    color: #4b5563;

    &:hover {
      background-color: rgba(255, 255, 255, 0.6);
    }
  }

  .chartContainer {
    background-color: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(209, 213, 219, 0.3);
  }

  .legend {
    background-color: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(209, 213, 219, 0.2);
  }

  .legendText {
    color: #4b5563;
  }

  .node {
    border: 1px solid rgba(209, 213, 219, 0.3);
  }

  .nodePhotoPlaceholder {
    background-color: rgba(229, 231, 235, 0.7);
    color: #4b5563;
  }

  .nodeName {
    color: #1f2937;
  }

  .nodeTitle {
    color: #4b5563;
  }
}
