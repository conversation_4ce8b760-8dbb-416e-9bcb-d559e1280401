import React from 'react';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';
import styles from './skeleton-loader.module.scss';

interface SkeletonLoaderProps {
  variant?: 'text' | 'card' | 'image' | 'button' | 'list-item' | 'top-candidate' | 'default-candidate' | 'welcome-content' | 'welcome-card';
  size?: 'small' | 'medium' | 'large' | 'title';
  width?: string;
  height?: string;
  count?: number;
  className?: string;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  variant = 'text',
  size = 'medium',
  width,
  height,
  count = 1,
  className = ''
}) => {
  const { theme } = useTheme();

  const renderTextSkeleton = () => {
    const sizeClass = size === 'small' ? styles.small : 
                     size === 'large' ? styles.large :
                     size === 'title' ? styles.title : '';
    
    return (
      <div 
        className={`${styles.textLine} ${sizeClass} ${className}`}
        style={{ width, height }}
      />
    );
  };

  const renderCardSkeleton = () => (
    <div className={`${styles.cardSkeleton} ${className}`} style={{ width, height }}>
      <div className={`${styles.textLine} ${styles.title}`} style={{ width: '60%' }} />
      <div className={styles.textLine} />
      <div className={styles.textLine} style={{ width: '80%' }} />
      <div className={styles.textLine} style={{ width: '70%' }} />
    </div>
  );

  const renderImageSkeleton = () => {
    const imageClass = size === 'large' ? styles.hero :
                      size === 'small' ? styles.avatar :
                      size === 'medium' ? styles.icon : '';
    
    return (
      <div 
        className={`${styles.imageSkeleton} ${imageClass} ${className}`}
        style={{ width, height }}
      />
    );
  };

  const renderButtonSkeleton = () => (
    <div 
      className={`${styles.buttonSkeleton} ${className}`}
      style={{ width, height }}
    />
  );

  const renderListItemSkeleton = () => (
    <div className={`${styles.listItemSkeleton} ${className}`} style={{ width, height }}>
      <div className={styles.leftContent}>
        <div className={`${styles.imageSkeleton} ${styles.avatar}`} />
        <div className={styles.textLine} style={{ width: '150px' }} />
      </div>
      <div className={styles.textLine} style={{ width: '80px' }} />
    </div>
  );

  const renderTopCandidateSkeleton = () => (
    <div className={`${styles.topCandidateSkeleton} ${className}`} style={{ width, height }}>
      <div className={styles.badgePlaceholder} />
      <div className={styles.trophyPlaceholder} />
      <div className={`${styles.textLine} ${styles.title}`} style={{ width: '70%' }} />
      <div className={styles.textLine} style={{ width: '50%' }} />
    </div>
  );

  const renderDefaultCandidateSkeleton = () => (
    <div className={`${styles.defaultCandidateSkeleton} ${className}`} style={{ width, height }}>
      <div className={styles.leftContent}>
        <div className={styles.placeBadge} />
        <div className={styles.nameText} />
      </div>
      <div className={styles.pointsText} />
    </div>
  );

  const renderWelcomeContentSkeleton = () => (
    <div className={`${styles.welcomeContentSkeleton} ${className}`} style={{ width, height }}>
      <div className={styles.textBlock}>
        <div className={styles.textLine} />
        <div className={styles.textLine} />
        <div className={styles.textLine} style={{ width: '75%' }} />
      </div>
      <div className={styles.textBlock}>
        <div className={styles.textLine} />
        <div className={styles.textLine} style={{ width: '85%' }} />
      </div>
    </div>
  );

  const renderWelcomeCardSkeleton = () => (
    <div className={`${styles.welcomeCardSkeleton} ${className}`} style={{ width, height }}>
      <div className={styles.iconPlaceholder} />
      <div className={styles.titlePlaceholder} />
      <div className={styles.contentLines}>
        <div className={styles.line} />
        <div className={styles.line} />
        <div className={styles.line} />
      </div>
    </div>
  );

  const renderSkeleton = () => {
    switch (variant) {
      case 'text':
        return renderTextSkeleton();
      case 'card':
        return renderCardSkeleton();
      case 'image':
        return renderImageSkeleton();
      case 'button':
        return renderButtonSkeleton();
      case 'list-item':
        return renderListItemSkeleton();
      case 'top-candidate':
        return renderTopCandidateSkeleton();
      case 'default-candidate':
        return renderDefaultCandidateSkeleton();
      case 'welcome-content':
        return renderWelcomeContentSkeleton();
      case 'welcome-card':
        return renderWelcomeCardSkeleton();
      default:
        return renderTextSkeleton();
    }
  };

  if (count === 1) {
    return renderSkeleton();
  }

  return (
    <>
      {Array.from({ length: count }, (_, index) => (
        <React.Fragment key={index}>
          {renderSkeleton()}
        </React.Fragment>
      ))}
    </>
  );
};

export default SkeletonLoader;
