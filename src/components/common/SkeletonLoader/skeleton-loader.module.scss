// Skeleton loader styles with gradient animations
.skeletonBase {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  transition: all 0.3s ease;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Container that maintains size during loading
.loadingContainer {
  transition: height 0.3s ease, min-height 0.3s ease;
  overflow: hidden;
}

// Text skeleton variants
.textLine {
  @extend .skeletonBase;
  height: 1rem;
  margin-bottom: 0.5rem;
  
  &.small {
    height: 0.875rem;
  }
  
  &.large {
    height: 1.25rem;
  }
  
  &.title {
    height: 2rem;
    margin-bottom: 1rem;
  }
}

// Card skeleton
.cardSkeleton {
  @extend .skeletonBase;
  padding: 1.5rem;
  border-radius: 0.75rem;
  backdrop-filter: blur(8px);
  min-height: 200px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

// Image skeleton
.imageSkeleton {
  @extend .skeletonBase;
  width: 100%;
  border-radius: 0.5rem;
  
  &.hero {
    height: 18rem; // h-72
    
    @media (min-width: 640px) {
      height: 24rem; // sm:h-96
    }
  }
  
  &.avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
  }
  
  &.icon {
    width: 2.75rem;
    height: 2.75rem;
    border-radius: 0.375rem;
  }
}

// Button skeleton
.buttonSkeleton {
  @extend .skeletonBase;
  height: 2.5rem;
  width: 6rem;
  border-radius: 0.375rem;
}

// List item skeleton
.listItemSkeleton {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
  }
}

// Leaderboard specific skeletons
.topCandidateSkeleton {
  @extend .cardSkeleton;
  align-items: center;
  text-align: center;
  min-height: 250px;
  
  .trophyPlaceholder {
    @extend .skeletonBase;
    width: 2.75rem;
    height: 2.75rem;
    border-radius: 50%;
    margin-bottom: 1rem;
  }
  
  .badgePlaceholder {
    @extend .skeletonBase;
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 2rem;
    height: 1.5rem;
    border-radius: 9999px;
  }
}

.defaultCandidateSkeleton {
  @extend .listItemSkeleton;
  justify-content: space-between;
  
  .leftContent {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
  }
  
  .placeBadge {
    @extend .skeletonBase;
    width: 2rem;
    height: 1.5rem;
    border-radius: 0.25rem;
  }
  
  .nameText {
    @extend .skeletonBase;
    height: 1rem;
    width: 8rem;
  }
  
  .pointsText {
    @extend .skeletonBase;
    height: 1rem;
    width: 4rem;
  }
}

// Welcome page specific skeletons
.welcomeContentSkeleton {
  padding: 2rem;
  
  .textBlock {
    margin-bottom: 1.5rem;
    
    .textLine {
      @extend .textLine;
      
      &:last-child {
        width: 75%;
      }
    }
  }
}

.welcomeCardSkeleton {
  @extend .cardSkeleton;
  
  .iconPlaceholder {
    @extend .skeletonBase;
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .titlePlaceholder {
    @extend .textLine.title;
    width: 80%;
  }
  
  .contentLines {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    
    .line {
      @extend .textLine;
      
      &:nth-child(1) { width: 100%; }
      &:nth-child(2) { width: 90%; }
      &:nth-child(3) { width: 70%; }
    }
  }
}

// Dark mode styles
:global(.dark) {
  .skeletonBase {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    background-size: 200% 100%;
  }
  
  .cardSkeleton {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .listItemSkeleton {
    background-color: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
}

// Light mode styles
:global(.light) {
  .skeletonBase {
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
    background-size: 200% 100%;
  }
  
  .cardSkeleton {
    background-color: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .listItemSkeleton {
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.05);
  }
}

// Responsive grid for skeleton cards
.skeletonGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  
  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

// Fade in animation when content loads
.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
