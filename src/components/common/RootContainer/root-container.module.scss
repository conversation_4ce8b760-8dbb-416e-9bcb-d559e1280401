.rootContainer {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 5px;
  box-shadow: 0 5px 15px 0 hsla(0, 0%, 0%, 0.2);
  padding: 0 15px 15px 15px;
  backdrop-filter: blur(10px);

  :global(.page-header) {
    margin: 20px 0;
    border-bottom: none;
  }

  :global(.panel),
  :global(.panel form .form-group:last-child) {
    margin-bottom: 0;
  }
}

// Dark mode styles
:global(.dark) {
  .rootContainer {
    background: rgba(0, 0, 0, 0.7);
    box-shadow: 0 5px 15px 0 hsla(0, 0%, 0%, 0.5);
  }
}

// Light mode styles
:global(.light) {
  .rootContainer {
    background: rgba(255, 255, 255, 0.9);
  }
}
