// Common component styles
.pageHeader {
  border-bottom: none;
  margin: 0;
}

.rootContainer {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 5px;
  box-shadow: 0 5px 15px 0 hsla(0, 0%, 0%, 0.2);
  padding: 0 15px 15px 15px;
  backdrop-filter: blur(10px);

  :global(.page-header) {
    margin: 20px 0;
    border-bottom: none;
  }

  :global(.panel),
  :global(.panel form .form-group:last-child) {
    margin-bottom: 0;
  }
}

// Table styles
.tableContainer {
  :global {
    .thead-light {
      background: white;
      border-color: white;
    }

    .table > thead > tr > th,
    .table > tbody > tr > th,
    .table > tfoot > tr > th,
    .table > thead > tr > td,
    .table > tbody > tr > td,
    .table > tfoot > tr > td {
      border-color: white;
    }

    .table-striped > tbody > tr:nth-of-type(odd) {
      background-color: #f5f5f5;
    }
  }
}

// Form styles
.formGroup {
  margin-bottom: 15px;
}

.formControl {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

// Button styles
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  border-radius: 4px;
  user-select: none;
}

.btnPrimary {
  composes: btn;
  color: #fff;
  background-color: #337ab7;
  border-color: #2e6da4;
}

.btnDanger {
  composes: btn;
  color: #fff;
  background-color: #d9534f;
  border-color: #d43f3a;
}

// Dark mode styles
:global(.dark) {
  .rootContainer {
    background: rgba(0, 0, 0, 0.7);
    box-shadow: 0 5px 15px 0 hsla(0, 0%, 0%, 0.5);
  }

  .tableContainer {
    :global {
      .thead-light {
        background: #333;
        border-color: #444;
      }

      .table > thead > tr > th,
      .table > tbody > tr > th,
      .table > tfoot > tr > th,
      .table > thead > tr > td,
      .table > tbody > tr > td,
      .table > tfoot > tr > td {
        border-color: #444;
      }

      .table-striped > tbody > tr:nth-of-type(odd) {
        background-color: #2a2a2a;
      }
    }
  }

  .formControl {
    background-color: #333;
    color: #eee;
    border-color: #555;
  }
}

// Light mode styles
:global(.light) {
  .rootContainer {
    background: rgba(255, 255, 255, 0.9);
  }
}
