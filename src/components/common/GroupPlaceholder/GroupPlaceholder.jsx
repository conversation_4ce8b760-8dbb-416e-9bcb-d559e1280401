import React from 'react';
import { useTheme } from '../../ThemeProvider/ThemeProvider';

/**
 * GroupPlaceholder - A reusable component for displaying challenge placeholders
 * when no photo/image is provided. Shows the challenge number with
 * alternating primary/secondary colors for visual distinction.
 *
 * @param {number} groupId - The group's ID/position number (for color alternation)
 * @param {string} groupName - The group's name (optional, for accessibility)
 * @param {string} size - Size variant: 'small', 'medium', 'large'
 * @param {string} theme - Theme override (optional, uses context if not provided)
 * @param {number} groupIndex - The group's index in the list (for color alternation)
 * @param {string} primaryColor - Client's primary/highlight color
 * @param {string} secondaryColor - Client's secondary color (optional, defaults to muted primary)
 * @param {boolean} isLevel - Whether this represents a level (true) or group (false)
 * @param {number} challengeNumber - The actual challenge number to display (#1, #2, etc.)
 */
const GroupPlaceholder = ({
  groupId,
  groupName,
  size = 'medium',
  theme: themeOverride,
  groupIndex,
  primaryColor,
  secondaryColor,
  isLevel = false,
  challengeNumber
}) => {
  const { theme: contextTheme } = useTheme();
  const theme = themeOverride || contextTheme;
  
  // Use challengeNumber if provided, otherwise fall back to group-based numbering
  const displayNumber = challengeNumber !== undefined ? challengeNumber : (
    isLevel ? groupId : (groupIndex !== undefined ? groupIndex + 1 : groupId)
  );

  // Use groupIndex for alternating colors
  const displayIndex = groupIndex !== undefined ? groupIndex : (groupId - (isLevel ? 1 : 0));

  // Determine if this group should use primary or secondary colors
  // Even indices use primary, odd use secondary for alternating pattern
  const isPrimary = displayIndex % 2 === 0;
  
  // Size configurations
  const sizeConfig = {
    small: {
      container: 'w-16 h-16',
      text: 'text-lg font-bold',
      padding: 'p-2'
    },
    medium: {
      container: 'w-24 h-24',
      text: 'text-xl font-bold',
      padding: 'p-3'
    },
    large: {
      container: 'w-[115px] h-[115px]',
      text: 'text-2xl font-bold',
      padding: 'p-4'
    }
  };
  
  const config = sizeConfig[size] || sizeConfig.medium;
  
  // Color schemes using client colors with alternation
  const getColorStyles = () => {
    // Use client colors if provided, otherwise fall back to theme colors
    const clientPrimary = theme === 'dark'
      ? (primaryColor || '#00A3E3') // Default blue if no client color
      : (primaryColor || '#0066CC'); // Darker blue for light mode

    // Create secondary color by adjusting the primary color or use provided secondary
    const clientSecondary = secondaryColor || (
      theme === 'dark'
        ? '#6B7280' // Gray for dark mode
        : '#9CA3AF'  // Lighter gray for light mode
    );

    const backgroundColor = isPrimary ? clientPrimary : clientSecondary;
    const textColor = theme === 'dark' ? '#FFFFFF' : '#FFFFFF'; // White text on colored backgrounds

    return {
      backgroundColor,
      color: textColor
    };
  };
  
  // Get the color styles for this group
  const colorStyles = getColorStyles();
  
  return (
    <div
      className={`
        ${config.container}
        ${config.padding}
        flex items-center justify-center
        rounded-full
        transition-all duration-200
        border-2 border-opacity-20
        ${theme === 'dark' ? 'border-white' : 'border-gray-800'}
      `}
      style={colorStyles}
      role="img"
      aria-label={challengeNumber !== undefined ? `Challenge ${displayNumber}` : (groupName ? `${isLevel ? 'Level' : 'Group'} ${displayNumber}: ${groupName}` : `${isLevel ? 'Level' : 'Group'} ${displayNumber}`)}
      title={challengeNumber !== undefined ? `Challenge ${displayNumber}` : (groupName ? `${isLevel ? 'Level' : 'Group'} ${displayNumber}: ${groupName}` : `${isLevel ? 'Level' : 'Group'} ${displayNumber}`)}
    >
      <span className={`${config.text} select-none`}>
        {challengeNumber !== undefined ? `#${displayNumber}` : displayNumber}
      </span>
    </div>
  );
};

export default GroupPlaceholder;
