import React from 'react';
import { useTheme } from './ThemeProvider';
import styles from './theme-provider.module.scss';

const ThemeToggle = () => {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <button 
      className={styles.themeToggle} 
      onClick={toggleTheme}
      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
    >
      {theme === 'dark' ? (
        <i className="glyphicon glyphicon-sun" />
      ) : (
        <i className="glyphicon glyphicon-moon" />
      )}
    </button>
  );
};

export default ThemeToggle;
