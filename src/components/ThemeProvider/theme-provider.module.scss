.themeToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

// Dark mode styles
:global(.dark) {
  .themeToggle {
    color: #fff;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

// Light mode styles
:global(.light) {
  .themeToggle {
    color: #333;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}
