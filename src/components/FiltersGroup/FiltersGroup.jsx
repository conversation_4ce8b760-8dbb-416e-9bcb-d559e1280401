import React, { useRef, useLayoutEffect, useState, useCallback } from "react";
import { useTheme } from "../ThemeProvider/ThemeProvider";
import styles from "./filters-group.module.scss";

const FiltersGroup = ({
  filters,
  currentFilter,
  className = "",
  backgroundColor,
  onClick = () => {},
}) => {
  const { theme } = useTheme();
  const wrapperRef = useRef(null);
  const [activeFilterStyles, setActiveFilterStyles] = useState({});

  useLayoutEffect(() => {
    if (!wrapperRef?.current) return;

    const currentFilterIndex = filters.findIndex(
      (f) => f.value === currentFilter
    );
    const currentElement = wrapperRef.current.childNodes[currentFilterIndex];

    if (!currentElement) return;

    requestAnimationFrame(() => {
      setActiveFilterStyles({
        transform: `translateX(${currentElement.offsetLeft}px)`,
        width: `${currentElement.offsetWidth}px`,
        backgroundColor,
      });
    });
  }, [filters, currentFilter, backgroundColor]);

  const handleFilterClick = useCallback(
    (filter) => {
      if (currentFilter === filter) return;
      onClick(filter);
    },
    [currentFilter, onClick]
  );

  return (
    <div className={styles.container}>
      <div className={`${styles.filtersWrapper} ${className}`} ref={wrapperRef}>
        {/* Sliding Selection Indicator */}
        <div
          className={styles.selectionIndicator}
          style={{
            ...activeFilterStyles,
          }}
        />

        {/* Filter Options */}
        {filters.map((filter) => {
          const { value, label } = filter;
          const isActive = currentFilter === value;

          return (
            <button
              key={value}
              onClick={() => handleFilterClick(value)}
              className={`${styles.filterButton} ${isActive ? styles.active : ''}`}
            >
              {label}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default FiltersGroup;
