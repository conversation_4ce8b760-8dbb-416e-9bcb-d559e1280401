.container {
  position: relative;
  width: fit-content;
  backdrop-filter: blur(8px);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.filtersWrapper {
  position: relative;
  display: flex;
  border-radius: 0.5rem;
}

.selectionIndicator {
  position: absolute;
  top: 0;
  bottom: 0;
  border-radius: 0.5rem;
  transition: all 0.3s ease-out;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 0;
}

.filterButton {
  position: relative;
  flex: 1;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: color 0.2s, background-color 0.2s;
  z-index: 10;
  
  &.active {
    color: #fff;
  }
  
  &:not(.active) {
    color: rgba(255, 255, 255, 0.7);
    
    &:hover {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.05);
    }
  }
}

// Dark mode styles
:global(.dark) {
  .container {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

// Light mode styles
:global(.light) {
  .container {
    background-color: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .filterButton {
    &.active {
      color: #fff;
    }
    
    &:not(.active) {
      color: rgba(0, 0, 0, 0.7);
      
      &:hover {
        color: rgba(0, 0, 0, 0.9);
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
  }
}
