import React from "react";
import { useTheme } from "../../../ThemeProvider/ThemeProvider";
import styles from "./default-candidate.module.scss";

const DefaultCandidate = ({ place, name, points }) => {
  const { theme } = useTheme();

  return (
    <div className={styles.container}>
      <div className={styles.infoContainer}>
        <span className={styles.placeBadge}>
          {place}
        </span>
        <span className={styles.name}>{name}</span>
      </div>
      <span className={styles.points}>{points} points</span>
    </div>
  );
};

export default DefaultCandidate;
