.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(8px);
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.infoContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.placeBadge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.name {
  font-weight: 500;
}

.points {
  font-size: 0.875rem;
}

// Dark mode styles
:global(.dark) {
  .container {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .placeBadge {
    background-color: rgba(255, 255, 255, 0.1);
    color: #d1d5db;
  }
  
  .name {
    color: #fff;
  }
  
  .points {
    color: #d1d5db;
  }
}

// Light mode styles
:global(.light) {
  .container {
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.1);
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
  
  .placeBadge {
    background-color: rgba(0, 0, 0, 0.1);
    color: #4b5563;
  }
  
  .name {
    color: #333;
  }
  
  .points {
    color: #4b5563;
  }
}
