import React from "react";
import { Trophy } from "lucide-react";
import { useTheme } from "../../../ThemeProvider/ThemeProvider";
import styles from "./top-candidate.module.scss";

const TopCandidate = ({ name, type, points, place }) => {
  const { theme } = useTheme();

  const getPlaceClass = (place) => {
    switch (place) {
      case 1:
        return styles.first;
      case 2:
        return styles.second;
      case 3:
        return styles.third;
      default:
        return "";
    }
  };

  const getTrophyColor = (place) => {
    switch (place) {
      case 1:
        return "#FFD700"; // Gold
      case 2:
        return "#C0C0C0"; // Silver
      case 3:
        return "#CD7F32"; // Bronze
      default:
        return "#ffffff"; // White
    }
  };

  if (type === "fake") return null;

  const placeClass = getPlaceClass(place);
  const trophyColor = getTrophyColor(place);

  return (
    <div className={`${styles.container} ${placeClass}`}>
      <div className={`${styles.card} ${placeClass}`}>
        {/* Place Badge */}
        <div className={`${styles.badge} ${placeClass}`}>
          #{place}
        </div>

        {/* Trophy Icon */}
        <div className={styles.trophyContainer}>
          <Trophy size={44} strokeWidth={1.5} style={{ color: trophyColor }} />
        </div>

        {/* User Info */}
        <div className={styles.infoContainer}>
          <h3 className={styles.name}>
            {name}
          </h3>
          <div className={`${styles.points} ${placeClass}`}>
            {points} points
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopCandidate;
