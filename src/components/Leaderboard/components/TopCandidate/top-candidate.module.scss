.container {
  position: relative;
  
  &.first {
    order: 2;
    transform: scale(1.1);
  }
  
  &.second {
    order: 1;
  }
  
  &.third {
    order: 3;
  }
}

.card {
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 2rem;
  border-radius: 0.75rem;
  backdrop-filter: blur(8px);
  transition: all 0.3s;
  
  &:hover {
    transform: scale(1.05);
  }
}

.badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.625rem;
  border-radius: 9999px;
  color: rgba(0, 0, 0, 0.8);
}

.trophyContainer {
  margin-bottom: 1rem;
}

.infoContainer {
  text-align: center;
}

.name {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.points {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

// Place-specific styles
.first {
  .card {
    background: rgba(255, 215, 0, 0.08);
    border: 1px solid rgba(234, 179, 8, 0.2);
  }
  
  .badge {
    background: linear-gradient(to right, #facc15, #eab308);
  }
  
  .points {
    background-color: rgba(234, 179, 8, 0.1);
    color: #fef08a;
  }
}

.second {
  .card {
    background: rgba(192, 192, 192, 0.08);
    border: 1px solid rgba(156, 163, 175, 0.2);
  }
  
  .badge {
    background: linear-gradient(to right, #d1d5db, #9ca3af);
  }
  
  .points {
    background-color: rgba(156, 163, 175, 0.1);
    color: #e5e7eb;
  }
}

.third {
  .card {
    background: rgba(205, 127, 50, 0.08);
    border: 1px solid rgba(217, 119, 6, 0.2);
  }
  
  .badge {
    background: linear-gradient(to right, #fbbf24, #d97706);
  }
  
  .points {
    background-color: rgba(217, 119, 6, 0.1);
    color: #fde68a;
  }
}

// Dark mode styles
:global(.dark) {
  .name {
    color: #fff;
  }
}

// Light mode styles
:global(.light) {
  .first {
    .card {
      background: rgba(255, 215, 0, 0.05);
      border: 1px solid rgba(234, 179, 8, 0.3);
    }
    
    .points {
      background-color: rgba(234, 179, 8, 0.1);
      color: #854d0e;
    }
  }
  
  .second {
    .card {
      background: rgba(192, 192, 192, 0.05);
      border: 1px solid rgba(156, 163, 175, 0.3);
    }
    
    .points {
      background-color: rgba(156, 163, 175, 0.1);
      color: #4b5563;
    }
  }
  
  .third {
    .card {
      background: rgba(205, 127, 50, 0.05);
      border: 1px solid rgba(217, 119, 6, 0.3);
    }
    
    .points {
      background-color: rgba(217, 119, 6, 0.1);
      color: #92400e;
    }
  }
  
  .name {
    color: #333;
  }
}
