import React, { useEffect, useState, useMemo } from "react";
import to from "await-to-js";

import FiltersGroup from "../FiltersGroup/FiltersGroup";
import DefaultCandidate from "./components/DefaultCandidate/DefaultCandidate";
import TopCandidate from "./components/TopCandidate/TopCandidate";
import Loader from "../common/Loader";
import { getLeaderboard } from "../../actions/user";
import { useTheme } from "../ThemeProvider/ThemeProvider";
import { toast } from "sonner";
import styles from "./leaderboard.module.scss";

const Leaderboard = ({ user, navigate }) => {
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [currentLeaderboardRegionId, setCurrentLeaderboardRegionId] =
    useState(-1);
  const [leaderboard, setLeaderboard] = useState({});

  const client = user?.client || {};
  const { leaderboardTabName, leaderboardTabVisibility, lightHighlightColor } =
    client;

  useEffect(() => {
    if (
      client?.hasOwnProperty("leaderboardTabVisibility") &&
      !leaderboardTabVisibility
    ) {
      navigate("/org-charts");
      return;
    }
    fetchLeaderboard();
  }, [client, leaderboardTabVisibility, navigate]);

  const fetchLeaderboard = async () => {
    setIsLoading(true);
    const [err, res] = await to(getLeaderboard().payload);

    if (err) {
      toast.error("Failed to load leaderboard");
      setIsLoading(false);
      return;
    }

    setLeaderboard(res.data);
    if (res.data.regions?.length) {
      setCurrentLeaderboardRegionId(res.data.regions[0].id);
    }
    setIsLoading(false);
  };

  const selectedLeaderboardRegion = useMemo(
    () =>
      leaderboard.regions?.find(({ id }) => id === currentLeaderboardRegionId),
    [leaderboard, currentLeaderboardRegionId]
  );

  const topCandidates = useMemo(() => {
    if (!selectedLeaderboardRegion) return [];
    const topUsers = selectedLeaderboardRegion.users.slice(0, 3);

    if (topUsers.length === 3) return topUsers;
    if (topUsers.length === 2) {
      return [...topUsers, { id: "FAKE_USER_1", type: "fake" }];
    }
    if (topUsers.length === 1) {
      return [
        ...topUsers,
        { id: "FAKE_USER_1", type: "fake" },
        { id: "FAKE_USER_2", type: "fake" },
      ];
    }
    return [];
  }, [selectedLeaderboardRegion]);

  const notTopCandidates = useMemo(() => {
    if (!selectedLeaderboardRegion) return [];
    return selectedLeaderboardRegion.users.slice(3);
  }, [selectedLeaderboardRegion]);

  const filters = useMemo(() => {
    if (!leaderboard?.regions?.length) return [];
    return leaderboard.regions.map(({ id, name }) => ({
      value: id,
      label: name,
    }));
  }, [leaderboard]);

  if (isLoading) return <Loader />;

  return (
    <div className={styles.container}>
      <LeaderboardHeader title={leaderboardTabName || "Leaderboard"} />

      {leaderboard?.regions?.length > 0 && (
        <FiltersGroup
          filters={filters}
          currentFilter={currentLeaderboardRegionId}
          backgroundColor={lightHighlightColor}
          onClick={setCurrentLeaderboardRegionId}
        />
      )}

      {selectedLeaderboardRegion?.users && (
        <div className={styles.section}>
          {/* Top 3 Candidates */}
          <div className={styles.topCandidatesContainer}>
            <h3 className={styles.sectionTitle}>Top Candidates</h3>
            <div className={styles.topCandidatesGrid}>
              {topCandidates.map((candidate, i) => (
                <TopCandidate key={i} {...candidate} place={i + 1} />
              ))}
            </div>
          </div>

          {/* Other Candidates */}
          {notTopCandidates.length > 0 && (
            <div className={styles.otherCandidatesContainer}>
              <h3 className={styles.sectionTitle}>
                Other Candidates
              </h3>
              <div className={styles.otherCandidatesList}>
                {notTopCandidates.map((candidate, i) => (
                  <DefaultCandidate key={i} {...candidate} place={4 + i} />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const LeaderboardHeader = ({ title }) => (
  <div className={styles.header}>
    <h1 className={`${styles.title} ${styles.titleGradient}`}>
      {title}
    </h1>
    <div className={styles.titleDivider} />
  </div>
);

export default Leaderboard;
