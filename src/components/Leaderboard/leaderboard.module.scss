.container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem;
  padding-bottom: 2rem;
}

.header {
  position: relative;
  padding-bottom: 0.5rem;
}

.title {
  font-size: 1.875rem;
  font-weight: 700;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.titleGradient {
  background-image: linear-gradient(to right, #ffffff, rgba(255, 255, 255, 0.6));
}

.titleDivider {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.1), transparent);
}

.section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.sectionTitle {
  font-size: 1.125rem;
  font-weight: 600;
}

.topCandidatesContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.topCandidatesGrid {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1rem;
  
  @media (min-width: 768px) {
    flex-direction: row;
  }
}

.otherCandidatesContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 0.75rem;
  backdrop-filter: blur(8px);
}

.otherCandidatesList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

// Dark mode styles
:global(.dark) {
  .title {
    color: #fff;
  }
  
  .sectionTitle {
    color: #fff;
  }
  
  .otherCandidatesContainer {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

// Light mode styles
:global(.light) {
  .title {
    color: #333;
  }
  
  .titleGradient {
    background-image: linear-gradient(to right, #333333, rgba(51, 51, 51, 0.6));
  }
  
  .titleDivider {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.1), transparent);
  }
  
  .sectionTitle {
    color: #333;
  }
  
  .otherCandidatesContainer {
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
}
