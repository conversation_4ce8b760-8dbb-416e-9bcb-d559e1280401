import { eq, sql, and, isNull, or } from 'drizzle-orm';
import { db } from '../index.js';
import { challengeGroups } from '../schema.js';

export const findChallengeGroupById = async (id) => {
  const [group] = await db
    .select({
      id: challengeGroups.id,
      name: challengeGroups.name,
      challenge_scheme_id: challengeGroups.challenge_scheme_id,
      created_at: challengeGroups.created_at,
    })
    .from(challengeGroups)
    .where(eq(challengeGroups.id, id))
    .limit(1);

  return group;
};

export const createChallengeGroup = async ({ name, challenge_scheme_id }) => {
  const [group] = await db
    .insert(challengeGroups)
    .values({
      name,
      challenge_scheme_id,
      created_at: sql`current_timestamp`,
    })
    .returning();

  return group;
};

export const deleteChallengeGroup = async (id) => {
  const [group] = await db
    .delete(challengeGroups)
    .where(eq(challengeGroups.id, id))
    .returning();

  return group;
};

export const listAllChallengeGroups = async () => {
  const groups = await db
    .select({
      id: challengeGroups.id,
      name: challengeGroups.name,
      challenge_scheme_id: challengeGroups.challenge_scheme_id,
      created_at: challengeGroups.created_at,
    })
    .from(challengeGroups)
    .orderBy(challengeGroups.name);

  return groups;
};

export const listChallengeGroupsByScheme = async (challenge_scheme_id) => {
  const groups = await db
    .select({
      id: challengeGroups.id,
      name: challengeGroups.name,
      challenge_scheme_id: challengeGroups.challenge_scheme_id,
      created_at: challengeGroups.created_at,
    })
    .from(challengeGroups)
    .where(
      or(
        eq(challengeGroups.challenge_scheme_id, challenge_scheme_id),
        isNull(challengeGroups.challenge_scheme_id) // Include global groups for backward compatibility
      )
    )
    .orderBy(challengeGroups.name);

  return groups;
};
