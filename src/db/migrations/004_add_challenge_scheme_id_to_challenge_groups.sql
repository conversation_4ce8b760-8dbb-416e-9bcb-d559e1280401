-- Migration to tie challenge groups to specific challenge schemas
-- This makes challenge groups schema-specific instead of global

-- Step 1: Add challenge_scheme_id column to challenge_groups table
ALTER TABLE challenge_groups 
ADD COLUMN challenge_scheme_id INTEGER REFERENCES lumen_challenge_scheme(scheme_id);

-- Step 2: Create index for better performance on schema-based queries
CREATE INDEX idx_challenge_groups_scheme_id ON challenge_groups(challenge_scheme_id);

-- Step 3: Handle existing data
-- Option A: Leave existing groups unassigned (null challenge_scheme_id) for backward compatibility
-- This allows existing groups to be visible across all schemas until explicitly assigned

-- Option B: If you want to assign existing groups to all existing schemas, uncomment below:
-- INSERT INTO challenge_groups (name, challenge_scheme_id, created_at)
-- SELECT cg.name, lcs.scheme_id, cg.created_at
-- FROM challenge_groups cg
-- CROSS JOIN lumen_challenge_scheme lcs
-- WHERE cg.challenge_scheme_id IS NULL;

-- Step 4: Add constraint to ensure group names are unique within each schema
-- (Optional - uncomment if you want to enforce unique names per schema)
-- ALTER TABLE challenge_groups 
-- ADD CONSTRAINT unique_group_name_per_schema 
-- UNIQUE (name, challenge_scheme_id);

-- Note: After this migration, you'll need to update your application code to:
-- 1. Filter challenge groups by challenge_scheme_id when listing groups for a schema
-- 2. Require challenge_scheme_id when creating new groups
-- 3. Validate that assigned groups belong to the same schema as the challenge
