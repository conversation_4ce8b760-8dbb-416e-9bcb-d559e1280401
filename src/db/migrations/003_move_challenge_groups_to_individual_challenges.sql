-- Migration to move group assignment from challenge schemes to individual challenges
-- This allows individual challenges to be assigned to groups instead of entire schemes

-- Step 1: Add group_id column to lumen_challenge table
ALTER TABLE lumen_challenge 
ADD COLUMN group_id INTEGER REFERENCES challenge_groups(id);

-- Step 2: Migrate existing group assignments from schemes to individual challenges
-- For each challenge scheme that has a group_id, assign that group to all challenges in the scheme
UPDATE lumen_challenge 
SET group_id = (
    SELECT lcs.group_id 
    FROM lumen_challenge_scheme lcs 
    WHERE lcs.scheme_id = lumen_challenge.challenge_scheme_id 
    AND lcs.group_id IS NOT NULL
);

-- Step 3: Remove group_id column from lumen_challenge_scheme table
ALTER TABLE lumen_challenge_scheme 
DROP COLUMN group_id;

-- Add index for performance on the new group_id column
CREATE INDEX idx_lumen_challenge_group_id ON lumen_challenge(group_id);
