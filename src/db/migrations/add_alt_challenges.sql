-- Add alt challenge flags to lumen_challenge table
ALTER TABLE lumen_challenge
ADD COLUMN option_a_is_alt boolean DEFAULT false;

ALTER TABLE lumen_challenge
ADD COLUMN option_b_is_alt boolean DEFAULT false;

ALTER TABLE lumen_challenge
ADD COLUMN option_c_is_alt boolean DEFAULT false;

ALTER TABLE lumen_challenge
ADD COLUMN is_alt_challenge boolean DEFAULT false;

ALTER TABLE lumen_challenge
ADD COLUMN alt_of_challenge integer;

-- Add comments for the new columns
COMMENT ON COLUMN lumen_challenge.option_a_is_alt IS 'True if option A leads to an alt challenge';
COMMENT ON COLUMN lumen_challenge.option_b_is_alt IS 'True if option B leads to an alt challenge';
COMMENT ON COLUMN lumen_challenge.option_c_is_alt IS 'True if option C leads to an alt challenge';
COMMENT ON COLUMN lumen_challenge.is_alt_challenge IS 'True if this challenge itself is an alt challenge';
COMMENT ON COLUMN lumen_challenge.alt_of_challenge IS 'ID of the challenge this is an alt version of';
