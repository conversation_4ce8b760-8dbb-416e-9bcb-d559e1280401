import {
  pgTable,
  serial,
  varchar,
  timestamp,
  boolean,
  text,
  integer,
  numeric,
  jsonb,
  json,
} from 'drizzle-orm/pg-core';

// Admin table
export const lumenAdmin = pgTable('lumen_admin', {
  admin_id: serial('admin_id').primaryKey(),
  admin_email: varchar('admin_email', { length: 127 }).notNull(),
  admin_password: varchar('admin_password', { length: 127 }),
  admin_created_at: timestamp('admin_created_at'),
});

// Team table
export const lumenTeam = pgTable('lumen_team', {
  team_id: serial('team_id').primaryKey(),
  team_initiative_scheme_id: integer('team_initiative_scheme_id'),
  team_challenge_scheme_id: integer('team_challenge_scheme_id'),
  team_disabled: boolean('team_disabled').default(false),
  team_name: varchar('team_name', { length: 127 }),
  team_email: varchar('team_email', { length: 127 }),
  team_password: varchar('team_password', { length: 127 }),
  team_created_at: timestamp('team_created_at'),
  team_goal1: text('team_goal1'),
  team_goal2: text('team_goal2'),
  team_goal3: text('team_goal3'),
  client_id: integer('client_id'),
});

// Team Revenue Numbers
export const lumenTeamRevenueNumber = pgTable('lumen_team_revenue_number', {
  number_id: serial('number_id').primaryKey(),
  number_fixed: boolean('number_fixed'),
  number_name: varchar('number_name', { length: 127 }),
  number_company_value: integer('number_company_value').notNull(),
  number_industry_value: integer('number_industry_value').notNull(),
  number_planned_value: integer('number_planned_value').notNull(),
  number_team_id: integer('number_team_id').references(() => lumenTeam.team_id),
});

// Team Metrics
export const lumenTeamMetric = pgTable('lumen_team_metric', {
  metric_id: serial('metric_id').primaryKey(),
  metric_fixed: boolean('metric_fixed'),
  metric_team_id: integer('metric_team_id')
    .notNull()
    .references(() => lumenTeam.team_id),
  metric_name: varchar('metric_name', { length: 127 }),
  metric_string_value: varchar('metric_string_value', { length: 127 }),
  metric_company_value: varchar('metric_company_value', { length: 127 }),
  metric_industry_value: varchar('metric_industry_value', { length: 127 }),
  metric_maximum: integer('metric_maximum'),
});

// User Metrics
export const lumenUserMetric = pgTable('lumen_user_metric', {
  user_metric_id: serial('user_metric_id').primaryKey(),
  user_metric_team_id: integer('user_metric_team_id')
    .notNull()
    .references(() => lumenTeam.team_id),
  user_metric_name: varchar('user_metric_name', { length: 127 }),
  user_metric_value: varchar('user_metric_value', { length: 127 }),
  user_string_value: varchar('user_string_value', { length: 127 }),
});

// Initiative Scheme
export const lumenInitiativeScheme = pgTable('lumen_initiative_scheme', {
  scheme_id: serial('scheme_id').primaryKey(),
  scheme_disabled: boolean('scheme_disabled').default(false),
  scheme_name: varchar('scheme_name', { length: 253 }).notNull(),
  scheme_created_at: timestamp('scheme_created_at'),
  scheme_initiative_number: integer('scheme_initiative_number').notNull(),
});

// Initiatives
export const lumenInitiative = pgTable('lumen_initiative', {
  initiative_id: serial('initiative_id').primaryKey(),
  initiative_scheme_id: integer('initiative_scheme_id')
    .notNull()
    .references(() => lumenInitiativeScheme.scheme_id),
  initiative_name: varchar('initiative_name', { length: 253 }).notNull(),
  initiative_description: text('initiative_description'),
  metric1: integer('metric1'),
  metric2: integer('metric2'),
  metric3: integer('metric3'),
});

// Challenge Scheme
export const lumenChallengeScheme = pgTable('lumen_challenge_scheme', {
  scheme_id: serial('scheme_id').primaryKey(),
  scheme_disabled: boolean('scheme_disabled').default(false),
  scheme_name: varchar('scheme_name', { length: 253 }).notNull(),
  scheme_created_at: timestamp('scheme_created_at'),
});

// Challenges
export const lumenChallenge = pgTable('lumen_challenge', {
  challenge_id: serial('challenge_id').primaryKey(),
  challenge_scheme_id: integer('challenge_scheme_id')
    .notNull()
    .references(() => lumenChallengeScheme.scheme_id),
  challenge_image_url: text('challenge_image_url'),
  challenge_description: text('challenge_description'),
  challenge_bubble_size: varchar('challenge_bubble_size', { length: 10 }).default('medium'),
  challenge_option_a: text('challenge_option_a'),
  challenge_consequence_a: text('challenge_consequence_a'),
  challenge_option_metric1_a: integer('challenge_option_metric1_a'),
  challenge_option_metric2_a: integer('challenge_option_metric2_a'),
  challenge_option_metric3_a: integer('challenge_option_metric3_a'),
  group_id: integer('group_id').references(() => challengeGroups.id),
  challenge_option_b: text('challenge_option_b'),
  challenge_consequence_b: text('challenge_consequence_b'),
  challenge_option_metric1_b: integer('challenge_option_metric1_b'),
  challenge_option_metric2_b: integer('challenge_option_metric2_b'),
  challenge_option_metric3_b: integer('challenge_option_metric3_b'),
  challenge_option_c: text('challenge_option_c'),
  challenge_consequence_c: text('challenge_consequence_c'),
  challenge_option_metric1_c: integer('challenge_option_metric1_c'),
  challenge_option_metric2_c: integer('challenge_option_metric2_c'),
  challenge_option_metric3_c: integer('challenge_option_metric3_c'),
  // Alt challenge fields - simple flags to mark if options lead to alt challenges
  option_a_is_alt: boolean('option_a_is_alt').default(false),
  option_b_is_alt: boolean('option_b_is_alt').default(false),
  option_c_is_alt: boolean('option_c_is_alt').default(false),
  // Flag to mark if this challenge itself is an alt challenge
  is_alt_challenge: boolean('is_alt_challenge').default(false),
  // ID of the challenge this is an alt version of (null if not an alt challenge)
  alt_of_challenge: integer('alt_of_challenge'),
});

// Team Selected Initiatives
export const lumenTeamSelectedInitiative = pgTable(
  'lumen_team_selected_initiative',
  {
    selected_id: serial('selected_id').primaryKey(),
    selected_initiative_id: integer('selected_initiative_id').references(
      () => lumenInitiative.initiative_id
    ),
    selected_team_id: integer('selected_team_id').references(
      () => lumenTeam.team_id
    ),
    client_id: integer('client_id'),
  }
);

// Team Selected Challenges
export const lumenTeamSelectedChallenge = pgTable(
  'lumen_team_selected_challenge',
  {
    selected_id: serial('selected_id').primaryKey(),
    selected_challenge_id: integer('selected_challenge_id').references(
      () => lumenChallenge.challenge_id
    ),
    selected_team_id: integer('selected_team_id').references(
      () => lumenTeam.team_id
    ),
    selected_option_a: boolean('selected_option_a').default(false),
    selected_option_b: boolean('selected_option_b').default(false),
    selected_option_c: boolean('selected_option_c').default(false),
    client_id: integer('client_id'),
  }
);

// Clients
export const clients = pgTable('clients', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  background_image: text('background_image'),
  logo_image: text('logo_image'),
  home_tab_name: varchar('home_tab_name', { length: 255 }),
  challenges_tab_name: varchar('challenges_tab_name', { length: 255 }),
  goals_tab_name: varchar('goals_tab_name', { length: 255 }),
  strategic_tab_name: varchar('strategic_tab_name', { length: 255 }),
  org_chart_tab_name: varchar('org_chart_tab_name', { length: 255 }),
  self_assessment_tab_name: varchar('self_assessment_tab_name', {
    length: 255,
  }),
  leaderboard_tab_name: varchar('leaderboard_tab_name', { length: 255 }),
  dark_highlight_color: varchar('dark_highlight_color', { length: 255 }),
  light_highlight_color: varchar('light_highlight_color', { length: 255 }),
  home_scheme_id: integer('home_scheme_id'),
  initiative_scheme_id: integer('initiative_scheme_id').references(
    () => lumenInitiativeScheme.scheme_id
  ),
  challenge_scheme_id: integer('challenge_scheme_id').references(
    () => lumenChallengeScheme.scheme_id
  ),
  leaderboard_scheme_id: integer('leaderboard_scheme_id'),
  org_chart_scheme_id: integer('org_chart_scheme_id'),
  self_assessment_scheme_id: integer('self_assessment_scheme_id'),
  disabled: boolean('disabled').default(false),
  created_at: timestamp('created_at'),
  home_tab_visibility: boolean('home_tab_visibility').default(true),
  goals_tab_visibility: boolean('goals_tab_visibility').default(true),
  challenges_tab_visibility: boolean('challenges_tab_visibility').default(true),
  initiatives_tab_visibility: boolean('initiatives_tab_visibility').default(
    true
  ),
  org_chart_tab_visibility: boolean('org_chart_tab_visibility').default(true),
  self_assessment_tab_visibility: boolean(
    'self_assessment_tab_visibility'
  ).default(true),
  leaderboard_tab_visibility: boolean('leaderboard_tab_visibility').default(
    true
  ),
  sign_up_email_domain: varchar('sign_up_email_domain', { length: 255 }),
  workshop_image: text('workshop_image'),
  is_sign_up_enabled: boolean('is_sign_up_enabled').default(false),
  decision_scheme_id: integer('decision_scheme_id').references(
    () => lumenDecisionScheme.scheme_id
  ),
  decision_tab_name: varchar('decision_tab_name', { length: 255 }),
  decision_tab_visibility: boolean('decision_tab_visibility').default(true),
  scheme_order: json('scheme_order').default([]),
  fte_max: numeric('fte_max'),
  investment_max: numeric('investment_max'),
  ai_summary_title: varchar('ai_summary_title', { length: 255 }),
});

// Team Clients (junction table)
export const teamClients = pgTable('team_clients', {
  id: serial('id').primaryKey(),
  team_id: integer('team_id')
    .notNull()
    .references(() => lumenTeam.team_id),
  client_id: integer('client_id')
    .notNull()
    .references(() => clients.id),
});

// Team Goals
export const teamGoals = pgTable('team_goals', {
  id: serial('id').primaryKey(),
  team_id: integer('team_id')
    .notNull()
    .references(() => lumenTeam.team_id),
  client_id: integer('client_id')
    .notNull()
    .references(() => clients.id),
  goal_1: text('goal_1'),
  goal_2: text('goal_2'),
  goal_3: text('goal_3'),
  created_at: timestamp('created_at'),
});

// Global Team Metrics Scheme
export const globalTeamMetricsScheme = pgTable('global_team_metrics_scheme', {
  id: serial('id').primaryKey(),
  client_id: integer('client_id')
    .notNull()
    .references(() => clients.id),
  name: varchar('name', { length: 255 }).notNull(),
  alias: varchar('alias', { length: 255 }),
  default_value: integer('default_value').default(0),
  created_at: timestamp('created_at'),
});

// Global Team Metrics
export const globalTeamMetric = pgTable('global_team_metric', {
  id: serial('id').primaryKey(),
  value: integer('value').default(0),
  global_team_metric_scheme_id: integer(
    'global_team_metric_scheme_id'
  ).references(() => globalTeamMetricsScheme.id),
  team_id: integer('team_id').references(() => lumenTeam.team_id),
  client_id: integer('client_id').references(() => clients.id),
  created_at: timestamp('created_at'),
});

// Self Assessment Scheme
export const selfAssessmentScheme = pgTable('self_assessment_scheme', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  disabled: boolean('disabled').default(false),
  created_at: timestamp('created_at'),
  updated_at: timestamp('updated_at'),
});

// Self Assessment Answers
export const selfAssessmentAnswers = pgTable('self_assessment_answers', {
  id: serial('id').primaryKey(),
  client_id: integer('client_id')
    .notNull()
    .references(() => clients.id),
  team_id: integer('team_id')
    .notNull()
    .references(() => lumenTeam.team_id),
  scheme_id: integer('scheme_id')
    .notNull()
    .references(() => selfAssessmentScheme.id),
  normal_quadrant_1_questions: numeric('normal_quadrant_1_questions'),
  normal_quadrant_2_questions: numeric('normal_quadrant_2_questions'),
  normal_quadrant_3_questions: numeric('normal_quadrant_3_questions'),
  normal_quadrant_4_questions: numeric('normal_quadrant_4_questions'),
  stress_quadrant_1_questions: numeric('stress_quadrant_1_questions'),
  stress_quadrant_2_questions: numeric('stress_quadrant_2_questions'),
  stress_quadrant_3_questions: numeric('stress_quadrant_3_questions'),
  stress_quadrant_4_questions: numeric('stress_quadrant_4_questions'),
  raw_answers: text('raw_answers'),
  created_at: timestamp('created_at'),
});

// Self Assessment Quadrants Config
export const selfAssessmentQuadrantsConfig = pgTable(
  'self_assessment_quadrants_config',
  {
    id: serial('id').primaryKey(),
    scheme_id: integer('scheme_id')
      .notNull()
      .references(() => selfAssessmentScheme.id),
    quadrant_1_name: varchar('quadrant_1_name', { length: 255 }),
    quadrant_2_name: varchar('quadrant_2_name', { length: 255 }),
    quadrant_3_name: varchar('quadrant_3_name', { length: 255 }),
    quadrant_4_name: varchar('quadrant_4_name', { length: 255 }),
    normal_quadrant_1_questions: text('normal_quadrant_1_questions'),
    normal_quadrant_2_questions: text('normal_quadrant_2_questions'),
    normal_quadrant_3_questions: text('normal_quadrant_3_questions'),
    normal_quadrant_4_questions: text('normal_quadrant_4_questions'),
    stress_quadrant_1_questions: text('stress_quadrant_1_questions'),
    stress_quadrant_2_questions: text('stress_quadrant_2_questions'),
    stress_quadrant_3_questions: text('stress_quadrant_3_questions'),
    stress_quadrant_4_questions: text('stress_quadrant_4_questions'),
    created_at: timestamp('created_at'),
    updated_at: timestamp('updated_at'),
  }
);

// Self Assessment PDF Pages Config
export const selfAssessmentPdfPagesConfig = pgTable(
  'self_assessment_pdf_pages_config',
  {
    id: serial('id').primaryKey(),
    scheme_id: integer('scheme_id')
      .notNull()
      .references(() => selfAssessmentScheme.id),
    cover_image: text('cover_image'),
    page_2_quad_1_pdf: text('page_2_quad_1_pdf'),
    page_2_quad_2_pdf: text('page_2_quad_2_pdf'),
    page_2_quad_3_pdf: text('page_2_quad_3_pdf'),
    page_2_quad_4_pdf: text('page_2_quad_4_pdf'),
    page_3_quad_1_pdf: text('page_3_quad_1_pdf'),
    page_3_quad_2_pdf: text('page_3_quad_2_pdf'),
    page_3_quad_3_pdf: text('page_3_quad_3_pdf'),
    page_3_quad_4_pdf: text('page_3_quad_4_pdf'),
    created_at: timestamp('created_at'),
    updated_at: timestamp('updated_at'),
  }
);

// Self Assessment PDF Paragraphs Config
export const selfAssessmentPdfParagraphsConfig = pgTable(
  'self_assessment_pdf_paragraphs_config',
  {
    id: serial('id').primaryKey(),
    scheme_id: integer('scheme_id')
      .notNull()
      .references(() => selfAssessmentScheme.id),
    normal_paragraphs: text('normal_paragraphs'),
    stress_paragraphs: text('stress_paragraphs'),
    created_at: timestamp('created_at'),
    updated_at: timestamp('updated_at'),
  }
);

// Org Chart Scheme
export const orgChartScheme = pgTable('org_chart_scheme', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  disabled: boolean('disabled').default(false),
  created_at: timestamp('created_at'),
});

// Org Chart
export const orgChart = pgTable('org_chart', {
  id: serial('id').primaryKey(),
  org_chart_scheme_id: integer('org_chart_scheme_id')
    .notNull()
    .references(() => orgChartScheme.id),
  created_at: timestamp('created_at'),
});

// Org Chart Type
export const orgChartType = pgTable('org_chart_type', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  type: varchar('type', { length: 255 }).notNull(),
  is_visible: boolean('is_visible').default(true),
  org_chart_id: integer('org_chart_id')
    .notNull()
    .references(() => orgChart.id),
  created_at: timestamp('created_at'),
});

// Org Chart User
export const orgChartUser = pgTable('org_chart_user', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  title: varchar('title', { length: 255 }),
  status: integer('status').default(0),
  photo: text('photo'),
  meet_1_text: text('meet_1_text'),
  meet_1_points: integer('meet_1_points'),
  meet_2_text: text('meet_2_text'),
  meet_2_points: integer('meet_2_points'),
  meet_3_text: text('meet_3_text'),
  meet_3_points: integer('meet_3_points'),
  org_chart_type_id: integer('org_chart_type_id')
    .notNull()
    .references(() => orgChartType.id),
  parent_id: integer('parent_id').references(() => orgChartUser.id),
  locked_by_id: integer('locked_by_id').references(() => orgChartUser.id),
  created_at: timestamp('created_at'),
});

// Team Selected Org Chart
export const teamSelectedOrgChart = pgTable('team_selected_org_chart', {
  id: serial('id').primaryKey(),
  org_chart_user_id: integer('org_chart_user_id')
    .notNull()
    .references(() => orgChartUser.id),
  team_id: integer('team_id')
    .notNull()
    .references(() => lumenTeam.team_id),
  client_id: integer('client_id')
    .notNull()
    .references(() => clients.id),
  meet_1: boolean('meet_1').default(false),
  meet_2: boolean('meet_2').default(false),
  meet_3: boolean('meet_3').default(false),
  meet_1_time: timestamp('meet_1_time'),
  meet_2_time: timestamp('meet_2_time'),
  meet_3_time: timestamp('meet_3_time'),
  created_at: timestamp('created_at'),
});

// Leaderboard Scheme
export const leaderboardScheme = pgTable('leaderboard_scheme', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  disabled: boolean('disabled').default(false),
  created_at: timestamp('created_at'),
});

// Leaderboard Region
export const leaderboardRegion = pgTable('leaderboard_region', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  leaderboard_scheme_id: integer('leaderboard_scheme_id')
    .notNull()
    .references(() => leaderboardScheme.id),
  created_at: timestamp('created_at'),
});

// Leaderboard User
export const leaderboardUser = pgTable('leaderboard_user', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  points: integer('points').default(0),
  leaderboard_region_id: integer('leaderboard_region_id')
    .notNull()
    .references(() => leaderboardRegion.id),
  created_at: timestamp('created_at'),
});

// Welcome Page
export const welcomePage = pgTable('welcome_page', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  text: text('text'),
  image: text('image'),
  disabled: boolean('disabled').default(false),
  created_at: timestamp('created_at'),
});

// Decision Scheme
export const lumenDecisionScheme = pgTable('lumen_decision_scheme', {
  scheme_id: serial('scheme_id').primaryKey(),
  scheme_disabled: boolean('scheme_disabled').default(false),
  scheme_name: varchar('scheme_name', { length: 253 }).notNull(),
  scheme_pnl: jsonb('scheme_pnl'),
  scheme_created_at: timestamp('scheme_created_at'),
  scheme_updated_at: timestamp('scheme_updated_at'),
  // AI Prompt Template fields
  global_pnl_prompt_template: text('global_pnl_prompt_template'), // Custom template for global P&L analysis
  use_custom_global_template: boolean('use_custom_global_template').default(false), // Whether to use custom template
});

// Decision Pages
export const lumenDecisionPage = pgTable('lumen_decision_page', {
  page_id: serial('page_id').primaryKey(),
  scheme_id: integer('scheme_id')
    .notNull()
    .references(() => lumenDecisionScheme.scheme_id),
  page_number: integer('page_number').notNull(),
  page_name: varchar('page_name', { length: 255 }),
  page_image: text('page_image'),
  sliders: jsonb('sliders').notNull(),
  incentives: jsonb('incentives').notNull(),
  page_pnl: jsonb('page_pnl'),
  // AI Prompt Template fields for per-page customization
  page_pnl_prompt_template: text('page_pnl_prompt_template'), // Custom template for this page's P&L analysis
  use_custom_page_template: boolean('use_custom_page_template').default(false), // Whether to use custom template for this page
});

// Decision Results table
export const decisionResults = pgTable('decision_results', {
  id: serial('id').primaryKey(),
  user_id: integer('user_id')
    .notNull()
    .references(() => lumenTeam.team_id),
  decision_scheme_id: integer('decision_scheme_id')
    .notNull()
    .references(() => lumenDecisionScheme.scheme_id),
  client_id: integer('client_id')
    .notNull()
    .references(() => clients.id),
  selected_values: json('selected_values').notNull(),
  total_fte: numeric('total_fte').notNull(),
  total_investment: numeric('total_investment').notNull(),
  ai_analysis: json('ai_analysis'),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// AI Prompt Logs table - tracks all AI API calls for debugging and analysis
export const aiPromptLogs = pgTable('ai_prompt_logs', {
  id: serial('id').primaryKey(),
  final_system_prompt: text('final_system_prompt').notNull(),
  final_question_prompt: text('final_question_prompt').notNull(),
  tokens_used: integer('tokens_used'),
  input_sources: json('input_sources').notNull(), // Array of data sources used in the prompt
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at'),
});

// Decision Groups
export const decisionGroups = pgTable('decision_groups', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  created_at: timestamp('created_at').defaultNow(),
});

// Challenge Groups
export const challengeGroups = pgTable('challenge_groups', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  challenge_scheme_id: integer('challenge_scheme_id').references(() => lumenChallengeScheme.scheme_id),
  created_at: timestamp('created_at').defaultNow(),
});
