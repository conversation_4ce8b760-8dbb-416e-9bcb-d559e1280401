import axios from 'axios';
import CONFIG from '../config';

export function getChallengeGroups() {
  return axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/challenge-groups`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
}

export function getChallengeGroupsByScheme(challengeSchemeId) {
  return axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/admin/challenge-groups/scheme/${challengeSchemeId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
}

export function createChallengeGroup(name, challengeSchemeId) {
  return axios({
    method: 'POST',
    url: `${CONFIG.API_URL}/admin/challenge-groups`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
      'Content-Type': 'application/json',
    },
    data: { name, challenge_scheme_id: challengeSchemeId },
  });
}

export function deleteChallengeGroup(id) {
  return axios({
    method: 'DELETE',
    url: `${CONFIG.API_URL}/admin/challenge-groups/id/${id}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
}
