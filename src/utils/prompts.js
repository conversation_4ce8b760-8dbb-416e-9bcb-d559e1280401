/**
 * @typedef {Object} PromptParams
 * @property {any} pnlData - Single PnL data object for individual analysis
 * @property {Object} userSelections
 * @property {number} userSelections.totalFTE
 * @property {number} userSelections.totalInvestment
 * @property {string} [customTemplate] - Optional custom prompt template
 * @property {Object} [templateVariables] - Additional variables for template substitution
 */

import { SYSTEM_PROMPTS } from '../../prompts/system.js';
import {
  TEMPLATE_VARIABLES,
  PNL_ANALYSIS_INSTRUCTIONS,
  PNL_RESPONSE_EXAMPLE,
  PNL_FORMAT_NOTE,
  DEFAULT_PNL_TEMPLATE
} from '../../prompts/pnl-templates.js';

export { SYSTEM_PROMPTS, TEMPLATE_VARIABLES };

/**
 * Substitutes template variables with actual values
 * @param {string} template - The template string with variables
 * @param {Object} variables - Object containing variable values
 * @returns {string} - Template with variables substituted
 */
const substituteTemplateVariables = (template, variables) => {
  let result = template;

  // Replace each variable in the template
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    result = result.replace(new RegExp(placeholder, 'g'), value);
  });

  return result;
};

/**
 * @param {PromptParams} params
 * @returns {string}
 */
/**
 * Converts a role-playing template into a proper P&L analysis template
 * @param {string} roleTemplate - The role-playing template
 * @returns {string} - Hybrid template with role context + P&L analysis structure
 */
const createHybridTemplate = (roleTemplate) => {
  return `${roleTemplate}

⚠️ CRITICAL P&L ANALYSIS REQUIREMENTS ⚠️

You must now perform a P&L analysis while maintaining your role perspective. Follow these requirements:

1. You MUST preserve the EXACT structure of the input P&L data
2. Do NOT create new column headers like 'Revenue', 'COGS', 'Year', 'Q1', 'Q2', etc.
3. Do NOT restructure or reorganize the table in any way
4. Simply add ONE column called "Adjusted PNL" to the existing structure
5. The output structure must be: [originalHeaders..., "Adjusted PNL"]
6. Each data row must be: [originalRowData..., calculatedValue]

You must respond with ONLY a valid JSON object in the following format:
{{RESPONSE_FORMAT}}

P&L Data to Analyze:
{{PNL_DATA}}

EXAMPLE TRANSFORMATION:
If input P&L has headers: ["Category", "Amount"]
And data rows: [["Revenue", "$1,000,000"], ["COGS", "$400,000"]]

Your output should be:
"updatedPnL": [
  ["Category", "Amount", "Adjusted PNL"],
  ["Revenue", "$1,000,000", "calculated_value_1"],
  ["COGS", "$400,000", "calculated_value_2"]
]

User Decisions Made:
{{USER_SELECTIONS}}

Detailed Breakdown:
- Total FTE Change: {{TOTAL_FTE}} employees
- Total Investment: {{FORMATTED_INVESTMENT}}
- Slider Selections: {{SLIDER_DETAILS}}
- Initiative Selections: {{INCENTIVE_DETAILS}}
- Page: {{PAGE_NAME}} (Scheme: {{SCHEME_NAME}})

Apply your role-specific expertise to calculate the financial impacts while strictly following the structure preservation requirements above.`;
};

export const generatePNLAnalysisPrompt = ({ pnlData, userSelections, customTemplate, templateVariables = {} }) => {
  // Validate and use custom template if provided, otherwise use default
  let template = DEFAULT_PNL_TEMPLATE;

  if (customTemplate) {
    // Basic validation of custom template
    if (typeof customTemplate === 'string' && customTemplate.trim().length > 0) {
      // Check if template contains essential placeholders
      if (customTemplate.includes('{{PNL_DATA}}') && customTemplate.includes('{{RESPONSE_FORMAT}}')) {
        template = customTemplate;
        console.log('Using validated custom template');
      } else {
        // Convert role-playing template to hybrid P&L analysis template
        template = createHybridTemplate(customTemplate);
        console.log('Converted role template to hybrid P&L analysis template');
      }
    } else {
      console.warn('Invalid custom template format, using default template');
    }
  }

  // Extract detailed selection information
  const sliderDetails = userSelections.sliderSelections ?
    Object.entries(userSelections.sliderSelections).map(([fieldName, selection]) =>
      `${fieldName}: ${selection.selectedLabel} (FTE: ${selection.fte}, Investment: $${Number(selection.investment).toLocaleString()})`
    ).join('\n  - ') : 'No slider selections';

  const incentiveDetails = userSelections.incentiveSelections ?
    userSelections.incentiveSelections.filter(Boolean).map((incentive, idx) =>
      `Initiative ${idx + 1}: ${incentive.label} (FTE: ${incentive.fte}, Investment: $${Number(incentive.investment).toLocaleString()})`
    ).join('\n  - ') : 'No incentive selections';

  // Format P&L data as a clear table structure for the AI
  const formatPnLForAI = (pnlData) => {
    if (!Array.isArray(pnlData) || pnlData.length === 0) {
      return "No P&L data provided";
    }

    // Clean data by removing carriage returns and extra whitespace
    const cleanData = pnlData.map(row =>
      row.map(cell => String(cell).replace(/\r/g, '').trim())
    );

    // Convert array of arrays to a readable table format
    const headers = cleanData[0];
    const rows = cleanData.slice(1);

    let tableString = `P&L Table Structure:\nHeaders: [${headers.map(h => `"${h}"`).join(', ')}]\n\nData Rows:\n`;
    rows.forEach((row, index) => {
      tableString += `Row ${index + 1}: [${row.map(cell => `"${cell}"`).join(', ')}]\n`;
    });

    return tableString;
  };

  // Prepare standard template variables
  const standardVariables = {
    PNL_DATA: formatPnLForAI(pnlData),
    TOTAL_FTE: userSelections.totalFTE,
    TOTAL_INVESTMENT: userSelections.totalInvestment,
    FORMATTED_INVESTMENT: `$${userSelections.totalInvestment?.toLocaleString() || '0'}`,
    USER_SELECTIONS: JSON.stringify(userSelections, null, 2),
    SLIDER_DETAILS: sliderDetails,
    INCENTIVE_DETAILS: incentiveDetails,
    PAGE_NAME: templateVariables.pageName || 'Unknown Page',
    SCHEME_NAME: templateVariables.schemeName || 'Unknown Scheme',
    RESPONSE_FORMAT: `{
    "updatedPnL": [
      [...originalHeaderRow, "Adjusted PNL"],
      [...originalDataRow1, calculatedAdjustment1],
      [...originalDataRow2, calculatedAdjustment2]
    ],
    "analysis": "Your detailed analysis here"
  }`,
    ANALYSIS_INSTRUCTIONS: PNL_ANALYSIS_INSTRUCTIONS,
    FORMAT_NOTE: PNL_FORMAT_NOTE,
    RESPONSE_EXAMPLE: PNL_RESPONSE_EXAMPLE,
  };

  // Merge with any additional template variables
  const allVariables = { ...standardVariables, ...templateVariables };

  // Substitute variables in the template
  return substituteTemplateVariables(template, allVariables);
};

/**
 * Gets the appropriate prompt template for a given scheme and page
 * @param {Object} schemeData - Decision scheme object with template fields
 * @param {Array} pagesData - Array of decision page objects with template fields
 * @param {number} pnlIndex - Index of the current PNL being processed (0 = main, 1+ = pages)
 * @param {Array} _pages - Original pages array from frontend (unused but kept for API compatibility)
 * @returns {string|null} - Custom template if available, null for default
 */
export const getPromptTemplate = (schemeData, pagesData, pnlIndex, _pages) => {
  // For main PNL (index 0), check scheme-level template
  if (pnlIndex === 0) {
    if (schemeData?.use_custom_global_template && schemeData?.global_pnl_prompt_template) {
      return schemeData.global_pnl_prompt_template;
    }
    return null;
  }

  // For page PNLs (index 1+), check page-level template
  const pageIndex = pnlIndex - 1;
  if (pagesData && pageIndex < pagesData.length) {
    const pageData = pagesData[pageIndex];
    if (pageData?.use_custom_page_template && pageData?.page_pnl_prompt_template) {
      return pageData.page_pnl_prompt_template;
    }
  }

  // Fallback to scheme-level template if page doesn't have custom template
  if (schemeData?.use_custom_global_template && schemeData?.global_pnl_prompt_template) {
    return schemeData.global_pnl_prompt_template;
  }

  // Return null to use default template
  return null;
};
